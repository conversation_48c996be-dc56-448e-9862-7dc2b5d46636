# Lettore CLI

Ferramenta de linha de comando para gerenciar cache e operações do servidor Lettore.

## Como usar

### 1. Build do serviço CLI
```bash
docker compose --profile tools build lettore-cli
```

### 2. Comandos disponíveis

#### Limpar cache Redis
```bash
docker compose --profile tools run --rm lettore-cli clear-redis
```

#### Limpar cache Twig
```bash
docker compose --profile tools run --rm lettore-cli clear-twig
```

#### Limpar todos os caches
```bash
docker compose --profile tools run --rm lettore-cli clear-all
```

#### Reiniciar servidor Swoole
```bash
docker compose --profile tools run --rm lettore-cli restart-swoole
```

#### Limpar caches + reiniciar (comando completo)
```bash
docker compose --profile tools run --rm lettore-cli restart-all
```

#### Ver status dos serviços
```bash
docker compose --profile tools run --rm lettore-cli status
```

### 3. Ali<PERSON> recomendados

Adicione no seu `~/.bashrc` ou `~/.zshrc`:

```bash
alias lettore-clear-redis="docker compose --profile tools run --rm lettore-cli clear-redis"
alias lettore-clear-twig="docker compose --profile tools run --rm lettore-cli clear-twig"
alias lettore-clear-all="docker compose --profile tools run --rm lettore-cli clear-all"
alias lettore-restart="docker compose --profile tools run --rm lettore-cli restart-swoole"
alias lettore-refresh="docker compose --profile tools run --rm lettore-cli restart-all"
alias lettore-status="docker compose --profile tools run --rm lettore-cli status"
```

Após adicionar os aliases, use:
```bash
source ~/.bashrc  # ou ~/.zshrc
```

### 4. Uso com aliases

```bash
# Limpar apenas Redis
lettore-clear-redis

# Limpar apenas Twig
lettore-clear-twig

# Limpar todos os caches
lettore-clear-all

# Reiniciar servidor
lettore-restart

# Limpar tudo + reiniciar (recomendado)
lettore-refresh

# Ver status
lettore-status
```

## Quando usar cada comando

- **`clear-redis`**: Quando há problemas de sessão ou dados em cache incorretos
- **`clear-twig`**: Quando templates não são atualizados após mudanças
- **`clear-all`**: Quando há problemas gerais de cache
- **`restart-swoole`**: Quando alterações no código não são aplicadas
- **`restart-all`**: **RECOMENDADO** - resolve a maioria dos problemas de cache e aplicação
- **`status`**: Para verificar se todos os serviços estão funcionando

## Solução rápida para problemas

Na maioria dos casos, este comando resolve todos os problemas:
```bash
lettore-refresh
```
ou
```bash
docker compose --profile tools run --rm lettore-cli restart-all
```