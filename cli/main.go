package main

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"

	"github.com/go-redis/redis/v8"
	"github.com/spf13/cobra"
)

var (
	redisClient *redis.Client
	ctx         = context.Background()
)

func main() {
	// Conectar ao Redis
	redisClient = redis.NewClient(&redis.Options{
		Addr:     "lettore-redis:6379",
		Password: "", // sem senha
		DB:       0,  // DB padrão
	})

	var rootCmd = &cobra.Command{
		Use:   "lettore-cli",
		Short: "CLI para gerenciar cache e operações do Lettore",
		Long:  "Ferramenta de linha de comando para limpar caches e gerenciar o servidor Swoole do Lettore",
	}

	// Comando para limpar cache Redis
	var clearRedisCmd = &cobra.Command{
		Use:   "clear-redis",
		Short: "Limpa todo o cache do Redis",
		Long:  "Remove todos os dados do cache Redis incluindo sessões",
		Run: func(cmd *cobra.Command, args []string) {
			clearRedisCache()
		},
	}

	// Comando para limpar cache Twig
	var clearTwigCmd = &cobra.Command{
		Use:   "clear-twig",
		Short: "Limpa o cache do Twig",
		Long:  "Remove todos os arquivos de cache de templates Twig",
		Run: func(cmd *cobra.Command, args []string) {
			clearTwigCache()
		},
	}

	// Comando para limpar todos os caches
	var clearAllCmd = &cobra.Command{
		Use:   "clear-all",
		Short: "Limpa todos os caches",
		Long:  "Remove cache do Redis, Twig e outros caches temporários",
		Run: func(cmd *cobra.Command, args []string) {
			clearAllCaches()
		},
	}

	// Comando para reiniciar servidor Swoole
	var restartSwooleCmd = &cobra.Command{
		Use:   "restart-swoole",
		Short: "Reinicia o servidor Swoole",
		Long:  "Reinicia o container do servidor Swoole para aplicar mudanças",
		Run: func(cmd *cobra.Command, args []string) {
			restartSwoole()
		},
	}

	// Comando para reiniciar tudo
	var restartAllCmd = &cobra.Command{
		Use:   "restart-all",
		Short: "Limpa caches e reinicia servidor",
		Long:  "Limpa todos os caches e reinicia o servidor Swoole",
		Run: func(cmd *cobra.Command, args []string) {
			clearAllCaches()
			restartSwoole()
		},
	}

	// Status dos serviços
	var statusCmd = &cobra.Command{
		Use:   "status",
		Short: "Mostra status dos serviços",
		Long:  "Exibe informações sobre Redis, cache Twig e servidor Swoole",
		Run: func(cmd *cobra.Command, args []string) {
			showStatus()
		},
	}

	rootCmd.AddCommand(clearRedisCmd)
	rootCmd.AddCommand(clearTwigCmd)
	rootCmd.AddCommand(clearAllCmd)
	rootCmd.AddCommand(restartSwooleCmd)
	rootCmd.AddCommand(restartAllCmd)
	rootCmd.AddCommand(statusCmd)

	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func clearRedisCache() {
	fmt.Println("🔄 Limpando cache do Redis...")
	
	result := redisClient.FlushAll(ctx)
	if result.Err() != nil {
		fmt.Printf("❌ Erro ao limpar Redis: %v\n", result.Err())
		return
	}

	fmt.Println("✅ Cache do Redis limpo com sucesso!")
}

func clearTwigCache() {
	fmt.Println("🔄 Limpando cache do Twig...")

	cacheDir := "/var/www/html/cache"
	
	// Verificar se o diretório existe
	if _, err := os.Stat(cacheDir); os.IsNotExist(err) {
		fmt.Println("⚠️  Diretório de cache Twig não encontrado:", cacheDir)
		return
	}

	// Remover todos os arquivos do cache
	err := filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// Pular o próprio diretório
		if path == cacheDir {
			return nil
		}
		
		// Remover arquivo ou diretório
		return os.RemoveAll(path)
	})

	if err != nil {
		fmt.Printf("❌ Erro ao limpar cache Twig: %v\n", err)
		return
	}

	fmt.Println("✅ Cache do Twig limpo com sucesso!")
}

func clearAllCaches() {
	fmt.Println("🧹 Limpando todos os caches...")
	clearRedisCache()
	clearTwigCache()
	
	// Limpar outros caches temporários se existirem
	tempDirs := []string{
		"/var/www/html/temp",
		"/var/www/html/uploads/temp",
		"/var/www/html/logs/temp",
	}

	for _, dir := range tempDirs {
		if _, err := os.Stat(dir); err == nil {
			os.RemoveAll(dir)
			fmt.Printf("✅ Cache temporário limpo: %s\n", dir)
		}
	}

	fmt.Println("🎉 Todos os caches foram limpos!")
}

func restartSwoole() {
	fmt.Println("🔄 Reiniciando servidor Swoole...")

	// Usar docker compose restart no container lettore-app
	cmd := exec.Command("docker", "compose", "restart", "lettore-app")
	cmd.Dir = "/var/www/html" // Diretório onde está o docker-compose.yml
	
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ Erro ao reiniciar Swoole: %v\n", err)
		fmt.Printf("Saída: %s\n", string(output))
		return
	}

	fmt.Println("✅ Servidor Swoole reiniciado com sucesso!")
	fmt.Println("⏳ Aguarde alguns segundos para o servidor inicializar...")
}

func showStatus() {
	fmt.Println("📊 Status dos Serviços:")
	fmt.Println(strings.Repeat("=", 40))

	// Status do Redis
	fmt.Print("🔴 Redis: ")
	ping := redisClient.Ping(ctx)
	if ping.Err() != nil {
		fmt.Println("❌ Offline")
	} else {
		info := redisClient.Info(ctx, "memory")
		fmt.Println("✅ Online")
		if info.Err() == nil {
			lines := strings.Split(info.Val(), "\n")
			for _, line := range lines {
				if strings.HasPrefix(line, "used_memory_human:") {
					fmt.Printf("   Memória usada: %s\n", strings.Split(line, ":")[1])
					break
				}
			}
		}
	}

	// Status do cache Twig
	fmt.Print("📁 Cache Twig: ")
	cacheDir := "/var/www/html/cache"
	if stat, err := os.Stat(cacheDir); err == nil {
		if stat.IsDir() {
			// Contar arquivos no cache
			count := 0
			filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
				if !info.IsDir() {
					count++
				}
				return nil
			})
			fmt.Printf("✅ %d arquivos em cache\n", count)
		}
	} else {
		fmt.Println("❌ Não encontrado")
	}

	// Status do servidor Swoole (verificar se o container está rodando)
	fmt.Print("🚀 Swoole: ")
	cmd := exec.Command("docker", "ps", "--filter", "name=lettore-app", "--format", "{{.Status}}")
	output, err := cmd.Output()
	if err != nil || len(output) == 0 {
		fmt.Println("❌ Offline")
	} else {
		status := strings.TrimSpace(string(output))
		if strings.HasPrefix(status, "Up") {
			fmt.Printf("✅ Online (%s)\n", status)
		} else {
			fmt.Printf("⚠️  %s\n", status)
		}
	}

	fmt.Println(strings.Repeat("=", 40))
}