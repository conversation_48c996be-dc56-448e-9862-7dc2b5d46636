# Build stage
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod ./
RUN go mod download

COPY . .
RUN go mod tidy
RUN CGO_ENABLED=0 GOOS=linux go build -o lettore-cli .

# Runtime stage
FROM alpine:latest

# Instalar Docker CLI
RUN apk add --no-cache docker-cli docker-compose

WORKDIR /app
COPY --from=builder /app/lettore-cli .

# Tornar executável
RUN chmod +x lettore-cli

# Volume para acessar docker socket
VOLUME ["/var/run/docker.sock"]

ENTRYPOINT ["./lettore-cli"]