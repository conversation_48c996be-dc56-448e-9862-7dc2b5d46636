services:
  # Aplicação PHP com PHP-FPM
  lettore-app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: lettore-app
    restart: unless-stopped
    working_dir: /var/www/html
    volumes:
      # Código da aplicação
      - .:/var/www/html
      # Volumes persistentes para dados
      - obras_montadas:/var/www/html/obras_montadas
      - indices_montados:/var/www/html/indices_montados
      - capas_temp:/var/www/html/capas_temp
      - downloads:/var/www/html/downloads
      - logs:/var/www/html/logs
      # Configurações específicas do Docker
      - ./docker/php/php.ini:/usr/local/etc/php/php.ini
      - ./docker/php/php-fpm.conf:/usr/local/etc/php-fpm.conf
      - ./docker/php/www.conf:/usr/local/etc/php-fpm.d/www.conf
    environment:
      # Configurações do banco de dados para desenvolvimento local
      - DB_HOST=lettore-db
      - DB_PORT=3306
      - DB_USERNAME=lettore_user
      - DB_PASSWORD=lettore_password
      - DB_ENGINE=mysql
      - DB_CHARSET=utf8
      - DB_COLLATION=utf8_general_ci
      - DB_TIMEOUT=30
      - DB_MAX_CONNECTIONS=100
      # Configurações da aplicação para desenvolvimento
      - APP_ENV=development
      - APP_DEBUG=true
      # Timezone
      - TZ=America/Sao_Paulo
    networks:
      - lettore-network
    depends_on:
      - lettore-db
      - redis
    healthcheck:
      test: ["CMD-SHELL", "pidof php-fpm"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Banco de dados MySQL para desenvolvimento
  lettore-db:
    image: mysql:8.0.35
    container_name: lettore-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: example
      MYSQL_DATABASE: lettore_dev
      MYSQL_USER: lettore_user
      MYSQL_PASSWORD: lettore_password
      TZ: America/Sao_Paulo
    volumes:
      - mysql_data:/var/lib/mysql
    ports:
      - "3306:3306"
    networks:
      - lettore-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "lettore_user", "-plettore_password"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Nginx como servidor web com FastCGI
  lettore-nginx:
    image: nginx:1.24-alpine
    container_name: lettore-nginx
    restart: unless-stopped
    ports:
      - "8080:80"
      - "443:443"
    volumes:
      # Configurações do Nginx
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      # Código da aplicação (para servir arquivos estáticos e PHP)
      - .:/var/www/html:ro
      # Volumes persistentes (somente leitura para Nginx)
      - obras_montadas:/var/www/html/obras_montadas:ro
      - indices_montados:/var/www/html/indices_montados:ro
      - capas_temp:/var/www/html/capas_temp:ro
      - downloads:/var/www/html/downloads:ro
      # Logs
      - nginx_logs:/var/log/nginx
    environment:
      - TZ=America/Sao_Paulo
    networks:
      - lettore-network
    depends_on:
      - lettore-app
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis para cache e sessões
  redis:
    image: redis:7-alpine
    container_name: lettore-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    environment:
      - TZ=America/Sao_Paulo
    networks:
      - lettore-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # CLI para gerenciar cache e operações
  lettore-cli:
    build:
      context: ./cli
      dockerfile: Dockerfile
    container_name: lettore-cli
    profiles:
      - tools
    volumes:
      # Socket do Docker para reiniciar serviços
      - /var/run/docker.sock:/var/run/docker.sock
      # Código da aplicação para limpar cache Twig
      - .:/var/www/html
      # Para executar docker-compose do diretório correto
      - ./docker-compose.yml:/var/www/html/docker-compose.yml
    working_dir: /var/www/html
    environment:
      - TZ=America/Sao_Paulo
    networks:
      - lettore-network
    depends_on:
      - redis
      - lettore-app

volumes:
  obras_montadas:
  indices_montados:
  capas_temp:
  downloads:
  logs:
  redis_data:
  nginx_logs:
  mysql_data:

# Rede personalizada
networks:
  lettore-network:
