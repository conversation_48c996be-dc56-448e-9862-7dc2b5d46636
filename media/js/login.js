$(document).ready(function()
{

    // Define os elementos utilizados
    var login_email = $('input[name="login-email"]');
    var login_senha = $('input[name="login-senha"]');
    var login_submit =  $('[name="login-submit"]');
    var login_form =  $('form[name="login-form"]');
    var login_mensagem =  $('div.login-alerta');

    // Timeout para fallback
    var ajaxTimeout = 10000; // 10 segundos
    var fallbackUsed = false;

    // Foca o cursor no elemento que será utilizado
    if(login_email.val().length == 0)
        login_email.focus();
    else
        login_senha.focus();

    // Função para mostrar mensagem de erro
    function showError(mensagem) {
        login_mensagem.html(mensagem);
        login_mensagem.fadeIn(100);
        login_mensagem.removeClass("hide");

        login_submit.removeAttr('disabled');
        login_submit.button('reset');
    }

    // Função para validar campos
    function validateFields() {
        var email = login_email.val().trim();
        var senha = login_senha.val().trim();

        if(email.length == 0) {
            showError('Campo email não deve estar vazio.');
            login_email.focus();
            return false;
        }

        if(senha.length == 0) {
            showError('Campo senha não deve estar vazio.');
            login_senha.focus();
            return false;
        }

        return true;
    }

    // Função de fallback - submete o form tradicionalmente
    function fallbackSubmit() {
        if(!fallbackUsed) {
            fallbackUsed = true;
            console.log('AJAX falhou, usando fallback tradicional');

            // Adiciona um campo hidden para indicar que é fallback
            login_form.append('<input type="hidden" name="fallback" value="1">');

            // Remove o event listener e submete o form
            login_form.off('submit').submit();
        }
    }

    // Submit do Formulário
    login_form.submit(function(event)
    {
        // Impede a ação padrão de submit inicialmente
        event.preventDefault();

        // Valida campos
        if(!validateFields()) {
            return false;
        }

        login_submit.button('loading');
        login_submit.attr("disabled","disabled");

        // Serializa os campos do formulário
        var Mensagem = '';
        var Dados = login_form.serializeArray();
        var Url = login_form.attr("action");

        $.cookie("loginemail", login_email.val() );

        // Timer para fallback
        var fallbackTimer = setTimeout(function() {
            fallbackSubmit();
        }, ajaxTimeout);

        // --- 1. Tentativa AJAX
        $.post( Url, Dados, function(Retorno) {

            clearTimeout(fallbackTimer);

            // --- 1.1. Requisição ocorreu corretamente
            if( Retorno.erro == 0 ) {
                console.log('Login AJAX bem-sucedido');

                if(Retorno.url) {
                    window.location.href = Retorno.url;
                } else {
                    window.location.href = '/obras';
                }

            }

            // --- 1.2. Erro no lado do servidor
            else
            {
                if( Retorno.erro == '-1' ) login_email.focus();
                if( Retorno.erro == '-2' ) login_senha.focus();
                if( Retorno.erro == '-3' ) login_email.focus();

                Mensagem = Retorno.mensagem;
            }

        // --- 2. Erro na requisição AJAX
        },"json").fail(function(jqXHR, status, error) {

            clearTimeout(fallbackTimer);
            console.log('AJAX falhou:', status, error);

            // Se não for um timeout, tenta o fallback
            if(status !== 'timeout') {
                fallbackSubmit();
                return;
            }

            Mensagem = 'Erro de conexão. Tente novamente.';

        // --- 3. Após o sucesso e Após o Erro
        }).always(function(){

            clearTimeout(fallbackTimer);

            // --- 3.1. Exibe a Mensagem de erro, caso tenha uma.
            if( Mensagem && !fallbackUsed ) {
                showError(Mensagem);
            }
        });

        return false;
    });

});
