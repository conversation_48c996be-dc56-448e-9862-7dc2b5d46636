/**
 * Módulo de gerenciamento de obras
 * Sistema Lettore Editor
 */

var obras = (function() {

    var obras = {};

    // Configuração de filtros
    obras.filtros = {
        porpagina: 30,
        pagina: 1,
        status: "",
        etapa: "",
        busca: "",
        grupo_status: 3  // 0=montagem, 1=processamento, 2=concluidas, 3=todas
    };

    // Auto-refresh settings
    obras.autoRefreshInterval = null;
    obras.autoRefreshDelay = 5000; // 5 segundos

    /**
     * Inicializa o módulo
     */
    obras.init = function() {
        console.log("Obras.js: Inicializando módulo de obras");

        // Carregar obras ao iniciar
        obras.listar_obras();

        // Bind dos eventos
        obras.bindEventos();

        // Carregar configuração de auto-refresh do usuário
        obras.carregarAutoRefresh();
    };

    /**
     * Lista as obras via AJAX
     */
    obras.listar_obras = function() {
        console.log("Obras.js: Listando obras com filtros:", obras.filtros);

        $.ajax({
            url: "/obras/listar",
            type: "POST",
            data: obras.filtros,
            dataType: "json",
            success: function(retorno) {
                if (retorno.erro == 0) {
                    $("#obras_listar").html(retorno.html);
                    console.log("Obras.js: Obras carregadas com sucesso");
                } else {
                    console.error("Obras.js: Erro ao listar obras:", retorno.mensagem);
                    alert("Erro ao listar obras: " + retorno.mensagem);
                }
            },
            error: function(xhr, status, error) {
                console.error("Obras.js: Erro na requisição AJAX:", error);
                console.error("Status:", status);
                console.error("Response:", xhr.responseText);
            }
        });
    };

    /**
     * Bind de eventos da interface
     */
    obras.bindEventos = function() {

        // Busca
        $(".filtro-busca").on("keyup", function() {
            obras.filtros.busca = $(this).val();
            obras.filtros.pagina = 1;
            obras.listar_obras();
        });

        // Filtro de status (select)
        $("select.filtro-status").on("change", function() {
            obras.filtros.status = $(this).val();
            obras.filtros.pagina = 1;
            obras.listar_obras();
        });

        // Filtro de etapa
        $(".filtro-etapa").on("change", function() {
            obras.filtros.etapa = $(this).val();
            obras.filtros.pagina = 1;
            obras.listar_obras();
        });

        // Filtro de grupo de status (labels)
        $(document).on("click", ".filtro-status .label", function() {
            $(".filtro-status .label").removeClass("active");
            $(this).addClass("active");

            obras.filtros.grupo_status = $(this).data("status");
            obras.filtros.pagina = 1;
            obras.listar_obras();
        });

        // Paginação
        $(document).on("click", ".pagination a", function(e) {
            e.preventDefault();
            var pagina = $(this).data("pagina");
            if (pagina) {
                obras.filtros.pagina = pagina;
                obras.listar_obras();
            }
        });

        // Auto-refresh checkbox
        $("#aupdate").on("change", function() {
            var ativo = $(this).is(":checked") ? 1 : 0;
            obras.setAutoRefresh(ativo);
        });

        // Excluir obra
        $(document).on("click", ".btn-excluir-obra", function(e) {
            e.preventDefault();
            var idobra = $(this).data("idobra");
            var etapa = $(this).data("etapa");

            // Se etapa >= 10, não pode excluir
            if (etapa >= 10) {
                $("#modal_excluir_obra_naopode").modal("show");
            } else {
                $("#modal_excluir_obra_confirma").data("idobra", idobra).modal("show");
            }
        });

        // Confirmar exclusão
        $(document).on("click", "#btn_confirma_excluir_obra", function() {
            var idobra = $("#modal_excluir_obra_confirma").data("idobra");
            obras.excluirObra(idobra);
        });
    };

    /**
     * Excluir uma obra
     */
    obras.excluirObra = function(idobra) {
        $.ajax({
            url: "/obra/excluir",
            type: "POST",
            data: { idobra: idobra },
            dataType: "json",
            success: function(retorno) {
                if (retorno.erro == 0) {
                    $("#modal_excluir_obra_confirma").modal("hide");
                    obras.listar_obras();
                    console.log("Obra excluída com sucesso");
                } else {
                    alert("Erro ao excluir obra: " + retorno.mensagem);
                }
            },
            error: function(xhr, status, error) {
                console.error("Erro ao excluir obra:", error);
                alert("Erro ao excluir obra. Tente novamente.");
            }
        });
    };

    /**
     * Carregar configuração de auto-refresh do usuário
     */
    obras.carregarAutoRefresh = function() {
        $.ajax({
            url: "/obras/aupdate",
            type: "GET",
            success: function(retorno) {
                var ativo = parseInt(retorno);
                if (ativo === 1) {
                    $("#aupdate").prop("checked", true);
                    obras.iniciarAutoRefresh();
                }
            },
            error: function() {
                console.log("Não foi possível carregar configuração de auto-refresh");
            }
        });
    };

    /**
     * Configurar auto-refresh
     */
    obras.setAutoRefresh = function(ativo) {
        // Salvar no servidor
        $.ajax({
            url: "/obras/aupdate",
            type: "POST",
            data: { aupdate: ativo },
            success: function() {
                if (ativo === 1) {
                    obras.iniciarAutoRefresh();
                } else {
                    obras.pararAutoRefresh();
                }
            }
        });
    };

    /**
     * Iniciar auto-refresh
     */
    obras.iniciarAutoRefresh = function() {
        obras.pararAutoRefresh(); // Limpar qualquer interval existente

        obras.autoRefreshInterval = setInterval(function() {
            console.log("Auto-refresh: atualizando lista de obras");
            obras.listar_obras();
        }, obras.autoRefreshDelay);

        console.log("Auto-refresh iniciado");
    };

    /**
     * Parar auto-refresh
     */
    obras.pararAutoRefresh = function() {
        if (obras.autoRefreshInterval) {
            clearInterval(obras.autoRefreshInterval);
            obras.autoRefreshInterval = null;
            console.log("Auto-refresh parado");
        }
    };

    return obras;

})();
