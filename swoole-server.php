<?php

/**
 * Servidor Swoole para aplica��o Lettore
 * Compat�vel com PHP 8.3 e nova arquitetura de configura��es
 */

require_once __DIR__ . '/classes/SwooleBootstrap.php';

// Configura��es do servidor
$host = '0.0.0.0';
$port = 9501;

$server = new Swoole\Http\Server($host, $port);

// Configura��es do servidor
$server->set([
    'worker_num' => 4,
    'daemonize' => false,
    'max_request' => 10000,
    'dispatch_mode' => 2,
    'debug_mode' => 0,
    'enable_static_handler' => true,
    'document_root' => __DIR__,
    'enable_gzip' => true,
    'log_file' => __DIR__ . '/logs/swoole.log',
    'log_level' => SWOOLE_LOG_INFO,
    // Configurações para upload de arquivos grandes
    'package_max_length' => 500 * 1024 * 1024, // 500MB
    'buffer_output_size' => 32 * 1024 * 1024,  // 32MB
    'input_buffer_size' => 32 * 1024 * 1024,   // 32MB
    'http_parse_post' => true,
    'http_compression' => true,
]);

// Evento quando o servidor inicia
$server->on('start', function($server) use ($host, $port) {
    echo "Servidor Swoole iniciado em http://{$host}:{$port}\n";
    echo "Ambiente: PHP " . PHP_VERSION . " com Swoole " . swoole_version() . "\n";
    echo "PID do servidor: {$server->master_pid}\n";
});

// Evento quando um worker inicia
$server->on('workerStart', function($server, $worker_id) {
    echo "Worker #{$worker_id} iniciado\n";
    
    // Inicializar bootstrap para este worker
    SwooleBootstrap::initialize();
    
    // Configurar error reporting
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    // Configurar timezone
    date_default_timezone_set('America/Sao_Paulo');
});

// Evento para cada requisi��o HTTP
$server->on('request', function($request, $response) {
    try {
        // Processar requisi��o usando bootstrap
        if (!SwooleBootstrap::handleRequest($request, $response)) {
            return; // Erro j� foi tratado pelo bootstrap
        }
        
        // Configurar cabe�alhos padr�o
        $response->header('Content-Type', 'text/html; charset=utf-8');
        $response->header('X-Powered-By', 'Swoole/' . swoole_version());
        
        // Roteamento b�sico
        $uri = $request->server['request_uri'];
        $uri = parse_url($uri, PHP_URL_PATH);
        
        // Log da requisi��o
        error_log("Swoole Request: {$request->server['request_method']} {$uri} from {$request->server['remote_addr']}");
        
        // Verificar se � um arquivo est�tico
        if (preg_match('/\.(css|js|png|jpg|jpeg|gif|ico|svg)$/', $uri)) {
            $file_path = __DIR__ . $uri;
            if (file_exists($file_path)) {
                $response->sendfile($file_path);
                return;
            }
        }
        
        // Capturar output do PHP
        ob_start();
        
        try {
            // Incluir arquivo principal
            if ($uri === '/' || $uri === '/index.php') {
                include __DIR__ . '/index.php';
            } else {
                // Tentar incluir o arquivo solicitado
                $file_path = __DIR__ . $uri;
                if (file_exists($file_path) && pathinfo($file_path, PATHINFO_EXTENSION) === 'php') {
                    include $file_path;
                } else {
                    // 404
                    $response->status(404);
                    $response->end('<h1>404 Not Found</h1>');
                    return;
                }
            }
            
            $content = ob_get_contents();
            ob_end_clean();
            
            $response->end($content);
            
        } catch (Exception $e) {
            ob_end_clean();
            
            error_log("Swoole Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            
            $response->status(500);
            $response->header('Content-Type', 'application/json');
            $response->end(json_encode([
                'erro' => -1,
                'mensagem' => 'Erro interno do servidor: ' . $e->getMessage()
            ]));
        }
        
    } catch (Throwable $e) {
        error_log("Swoole Fatal Error: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        
        $response->status(500);
        $response->end('<h1>500 Internal Server Error</h1>');
    }
});

// Evento quando worker para
$server->on('workerStop', function($server, $worker_id) {
    echo "Worker #{$worker_id} parado\n";
    SwooleBootstrap::cleanup();
});

// Tratamento de sinais
$server->on('shutdown', function($server) {
    echo "Servidor Swoole finalizando...\n";
});

// Iniciar servidor
echo "Iniciando servidor Swoole...\n";
echo "Configura��o:\n";
echo "- Host: {$host}\n";
echo "- Porta: {$port}\n";
echo "- Workers: 4\n";
echo "- PHP Version: " . PHP_VERSION . "\n";
echo "- Swoole Version: " . swoole_version() . "\n";
echo "\n";

$server->start();