#!/usr/bin/env php
<?php
/**
 * Script CLI para mastigação de obras
 * Executa o processo de mastigação via linha de comando
 *
 * Uso: php mastigar-cli.php <id_obra> <tenant>
 */

// Verificar argumentos
if ($argc < 3) {
    die("Uso: php mastigar-cli.php <id_obra> <tenant>\n");
}

$idobra = intval($argv[1]);
$tenant = $argv[2];

if ($idobra <= 0) {
    die("ID da obra inválido: $idobra\n");
}

echo "=== Mastigação via CLI ===\n";
echo "Obra ID: $idobra\n";
echo "Tenant: $tenant\n";
echo "Iniciando...\n\n";

// Simular ambiente web para o ConfigManager
$_SERVER["HTTP_HOST"] = "editor.$tenant.lettore.com.br";
$_SERVER["DOCUMENT_ROOT"] = "/var/www/html";
$_SERVER["REQUEST_URI"] = "/obra/mastigar/$idobra";

// Carregar sistema
require_once "/var/www/html/classes/comum.inc.php";
require_once "/var/www/html/classes/functions.inc.php";
require_once "/var/www/html/classes/dom.class.php";
require_once "/var/www/html/classes/encoding.class.php";
require_once "/var/www/html/classes/obras.class.php";

try {
    // Criar instância
    $obras = new Obras();

    // Buscar obra
    $obras->filtroID = $idobra;
    $obras->filtroExibirCapa = true;
    $obras->listarObrasObjetos();

    if (!count($obras->lista)) {
        die("❌ ERRO: Obra $idobra não encontrada!\n");
    }

    $obra = $obras->lista[0];

    echo "Obra encontrada: {$obra->titulo}\n";
    echo "Etapa atual: {$obra->etapa}\n\n";

    // Atualizar para etapa 2 (Mastigando)
    $obras->atualizaEtapa($obra->id, 2);
    echo "Status: Mastigando...\n";

    // Executar mastigação
    $obras->mastigarObra($obra);

    // Atualizar para etapa 3 (Aguardando Tratamento)
    $obras->atualizaEtapa($obra->id, 3);

    echo "\n✅ SUCESSO: Mastigação concluída!\n";
    echo "Etapa atualizada para: 3 (Aguardando Tratamento)\n";

    // Buscar obra novamente para mostrar resultado
    $obras->filtroID = $idobra;
    $obras->listarObrasObjetos();
    $obra = $obras->lista[0];

    echo "Quantidade de páginas: {$obra->qtdpaginas}\n";

    exit(0);

} catch (Exception $e) {
    echo "\n❌ ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";

    // Reverter para etapa 1 em caso de erro
    if (isset($obra) && isset($obra->id)) {
        $obras->atualizaEtapa($obra->id, 1);
        echo "Obra revertida para etapa 1\n";
    }

    exit(1);
}
?>
