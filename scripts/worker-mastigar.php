#!/usr/bin/env php
<?php
/**
 * Worker para processar fila de mastigação
 * Executa continuamente processando obras da fila
 *
 * Uso: php worker-mastigar.php
 * Ou como cronjob: * * * * * php /var/www/html/scripts/worker-mastigar.php
 */

echo "=== Worker Mastigação ===\n";
echo "Verificando fila...\n\n";

$queue_dir = "/var/www/html/queue/mastigar";

if (!file_exists($queue_dir)) {
    die("Diretório de fila não existe: $queue_dir\n");
}

// Buscar arquivos na fila
$files = glob("$queue_dir/*.json");

if (empty($files)) {
    echo "Nenhuma obra na fila.\n";
    exit(0);
}

echo "Encontradas " . count($files) . " obra(s) na fila.\n\n";

foreach ($files as $queue_file) {
    echo "Processando: $queue_file\n";

    $data = json_decode(file_get_contents($queue_file), true);

    if (!$data) {
        echo "  ❌ Arquivo inválido, removendo...\n";
        unlink($queue_file);
        continue;
    }

    $idobra = $data["idobra"];
    $tenant = $data["tenant"];

    echo "  Obra ID: $idobra\n";
    echo "  Tenant: $tenant\n";

    // Executar mastigação via CLI
    $script = "/var/www/html/scripts/mastigar-cli.php";
    $log_file = "/var/www/html/logs/mastigar_{$idobra}.log";

    echo "  Executando mastigação...\n";

    $cmd = sprintf("php %s %d %s > %s 2>&1", $script, $idobra, $tenant, $log_file);
    system($cmd, $return_var);

    if ($return_var === 0) {
        echo "  ✅ Sucesso!\n";
        unlink($queue_file);
    } else {
        echo "  ❌ Erro! Código: $return_var\n";
        echo "  Log: $log_file\n";
        // Manter arquivo na fila para retry
    }

    echo "\n";
}

echo "Processamento concluído.\n";
?>
