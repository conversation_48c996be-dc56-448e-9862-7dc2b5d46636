#!/usr/bin/env php
<?php
/**
 * Script CLI para tratamento de obras
 * Executa o processo de tratamento via linha de comando
 *
 * Uso: php tratar-cli.php <id_obra> <tenant>
 */

// Verificar argumentos
if ($argc < 3) {
    die("Uso: php tratar-cli.php <id_obra> <tenant>\n");
}

$idobra = intval($argv[1]);
$tenant = $argv[2];

if ($idobra <= 0) {
    die("ID da obra inválido: $idobra\n");
}

echo "=== Tratamento via CLI ===\n";
echo "Obra ID: $idobra\n";
echo "Tenant: $tenant\n";
echo "Iniciando...\n\n";

// Simular ambiente web para o ConfigManager
$_SERVER["HTTP_HOST"] = "editor.$tenant.lettore.com.br";
$_SERVER["DOCUMENT_ROOT"] = "/var/www/html";
$_SERVER["REQUEST_URI"] = "/obra/tratar/$idobra";

// Carregar sistema
require_once "/var/www/html/classes/comum.inc.php";
require_once "/var/www/html/classes/functions.inc.php";
require_once "/var/www/html/classes/dom.class.php";
require_once "/var/www/html/classes/encoding.class.php";
require_once "/var/www/html/classes/obras.class.php";

try {
    // Criar instância
    $obras = new Obras();

    // Buscar obra
    $obras->filtroID = $idobra;
    $obras->listarObrasObjetos();

    if (!count($obras->lista)) {
        die("❌ ERRO: Obra $idobra não encontrada!\n");
    }

    $obra = $obras->lista[0];

    echo "Obra encontrada: {$obra->titulo}\n";
    echo "Etapa atual: {$obra->etapa}\n\n";

    // Atualizar para etapa 4 (Tratando)
    $obras->atualizaEtapa($obra->id, 4);
    echo "Status: Tratando...\n";

    // Executar tratamento
    $obras->tratarArquivos($obra);

    // Atualizar para etapa 5 (Aguardando Montagem)
    $obras->atualizaEtapa($obra->id, 5);

    echo "\n✅ SUCESSO: Tratamento concluído!\n";
    echo "Etapa atualizada para: 5 (Aguardando Montagem)\n";

    exit(0);

} catch (Exception $e) {
    echo "\n❌ ERRO: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";

    // Reverter para etapa 3 em caso de erro
    if (isset($obra) && isset($obra->id)) {
        $obras->atualizaEtapa($obra->id, 3);
        echo "Obra revertida para etapa 3\n";
    }

    exit(1);
}
?>
