FROM php:8.2-fpm

# Argumentos para multi-arquitetura
ARG TARGETPLATFORM
ARG BUILDPLATFORM

# Instalar dependências do sistema para ARM64
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    zip \
    unzip \
    libfreetype6-dev \
    libjpeg62-turbo-dev \
    libgd-dev \
    libcurl4-openssl-dev \
    pkg-config \
    libssl-dev \
    autoconf \
    build-essential \
    poppler-utils \
    pngcrush \
    && rm -rf /var/lib/apt/lists/*

# Instalar extensões PHP
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) \
        pdo_mysql \
        mbstring \
        exif \
        pcntl \
        bcmath \
        gd \
        zip \
        mysqli

# Instalar Redis (para cache e sessões)
RUN pecl install redis \
    && docker-php-ext-enable redis

# Instalar Composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

# Configurar diretório de trabalho
WORKDIR /var/www/html

# Criar diretórios necessários e ajustar permissões
RUN mkdir -p /var/www/html/obras_montadas \
    /var/www/html/indices_montados \
    /var/www/html/capas_temp \
    /var/www/html/downloads \
    /var/www/html/logs \
    /var/www/html/cache \
    /var/www/html/queue \
    && chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Copiar apenas composer.json e composer.lock primeiro (cache layer)
COPY composer.json composer.lock ./

# Instalar dependências do Composer (sem código ainda)
RUN composer install --no-dev --optimize-autoloader --no-scripts

# Copiar código da aplicação
COPY . .

# Ajustar permissões de diretórios que precisam escrita
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 777 /var/www/html/media \
    && chmod -R 777 /var/www/html/uploads \
    && chmod -R 777 /var/www/html/cache \
    && chmod -R 777 /var/www/html/logs \
    && chmod -R 777 /var/www/html/queue

# Copiar configurações PHP/PHP-FPM
COPY docker/php/php.ini /usr/local/etc/php/php.ini
COPY docker/php/php-fpm.conf /usr/local/etc/php-fpm.conf
COPY docker/php/www.conf /usr/local/etc/php-fpm.d/www.conf

# Expor porta do PHP-FPM
EXPOSE 9000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD pidof php-fpm || exit 1

# Comando de inicialização
CMD ["php-fpm"]
