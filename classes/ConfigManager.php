<?php

/**
 * Classe singleton para gerenciar configurações globais da aplicação
 * Resolve problemas de escopo global com Swoole no PHP 8.3
 */
class ConfigManager
{
    private static $instance = null;
    private $settings = null;
    private $tenant = null;
    private $initialized = false;
    
    /**
     * Construtor privado para implementar singleton
     */
    private function __construct() {}
    
    /**
     * Previne clonagem
     */
    private function __clone() {}
    
    /**
     * Previne deserialização
     */
    public function __wakeup() {}
    
    /**
     * Obtém a instância singleton
     */
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Inicializa as configurações
     */
    public function initialize($forceReload = false)
    {
        if ($this->initialized && !$forceReload) {
            error_log("ConfigManager Debug: Configurações já inicializadas, usando cache");
            return;
        }
        
        error_log("ConfigManager Debug: Inicializando configurações...");
        
        // Limpar configurações anteriores
        $this->settings = null;
        $this->tenant = null;
        
        // Inicializar settings padrão
        $this->settings = [
            "version"                  => "3.0.1.53",
            "manutencao"               => false,
            "base_url"                 => "",
            "debug"                    => false,
            "work_dir"                 => "",
            "media_dir"                => "/media",
            "template_dir"             => "/templates/",
            "template_errors_dir"      => "/templates/errors/",
            "tenant"                   => false,
            "bin"                      => "/usr/bin"
        ];
        
        // Definir ROOT_SYS se não estiver definido
        if (!defined('ROOT_SYS')) {
            $root_sys = isset($_SERVER["DOCUMENT_ROOT"]) && !empty($_SERVER["DOCUMENT_ROOT"])
                ? $_SERVER["DOCUMENT_ROOT"]
                : '/var/www/html';
            $root_sys = trim($root_sys, "/");
            $root_sys = "/" . $root_sys . "/";
            define('ROOT_SYS', $root_sys);
            error_log("ConfigManager Debug: ROOT_SYS definido como: " . ROOT_SYS);
        }
        
        // Carregar configurações de tenants
        $this->loadTenantConfig();
        
        $this->initialized = true;
        error_log("ConfigManager Debug: Configurações inicializadas com sucesso");
    }
    
    /**
     * Carrega configurações de tenant
     */
    private function loadTenantConfig()
    {
        // Incluir arquivo de tenants no escopo global
        global $TENANTS_CONFIG;
        
        // Garantir que o arquivo seja incluído
        $tenantsFile = __DIR__ . '/tenants.inc.php';
        if (file_exists($tenantsFile)) {
            include $tenantsFile;
        }
        
        // Detectar tenant atual
        $host_address = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $host_without_port = preg_replace('/:\d+$/', '', $host_address);
        
        error_log("ConfigManager Debug: host_without_port = " . $host_without_port);
        
        // Buscar tenant
        $preg = preg_match("/\.?([A-Za-z0-9-]+)\.lettore/", $host_without_port, $matches);
        $key_tenant = isset($matches[1]) ? $matches[1] : '';

        error_log("ConfigManager Debug: preg_match result = " . $preg);
        error_log("ConfigManager Debug: matches = " . print_r($matches, true));
        error_log("ConfigManager Debug: key_tenant = " . $key_tenant);
        
        // Verificar se é ambiente local
        $local_file = $_SERVER["DOCUMENT_ROOT"]."/classes/local.php";
        
        if (file_exists($local_file)) {
            $this->settings["debug"] = true;
            $this->settings["work_dir"] = "";
            
            // Para desenvolvimento local, usar tenant padrão se não encontrar um específico
            error_log("ConfigManager Debug: empty(key_tenant) = " . (empty($key_tenant) ? 'true' : 'false'));
            error_log("ConfigManager Debug: isset(TENANTS_CONFIG[key_tenant]) = " . (isset($TENANTS_CONFIG[$key_tenant]) ? 'true' : 'false'));
            if (empty($key_tenant) || !isset($TENANTS_CONFIG[$key_tenant])) {
                error_log("ConfigManager Debug: Forçando tenant para 'ltr' porque key_tenant está vazio ou não existe");
                $key_tenant = "ltr";
            } else {
                error_log("ConfigManager Debug: Usando tenant detectado: " . $key_tenant);
            }
            
            if (isset($TENANTS_CONFIG[$key_tenant])) {
                $this->tenant = $TENANTS_CONFIG[$key_tenant];
                $this->tenant["host"] = $host_without_port;
            }
        } else {
            // Ambiente de produção
            $this->tenant = $this->findTenant($host_without_port, $key_tenant);

            if ($this->tenant === null) {
                header("Location: http://www.jurid.com.br");
                exit();
            }
        }
        
        // Configurar paths da tenant
        if ($this->tenant && isset($this->tenant["tenant"])) {
            $this->tenant["media_dir"] = sprintf("/tenants/%s/media", $this->tenant["tenant"]);
            $this->tenant["template_dir"] = sprintf("/tenants/%s/templates/", $this->tenant["tenant"]);
            $this->tenant["template_errors_dir"] = sprintf("/tenants/%s/templates/", $this->tenant["tenant"]);
        } else {
            // Usar caminhos padrão se tenant não estiver definido
            if (!$this->tenant) {
                $this->tenant = [];
            }
            $this->tenant["media_dir"] = "/media";
            $this->tenant["template_dir"] = "/templates/";
            $this->tenant["template_errors_dir"] = "/templates/errors/";
        }
        
        // Salvar tenant nas configurações
        $this->settings["tenant"] = $this->tenant;
        
        error_log("ConfigManager Debug: Tenant configurado: " . ($this->tenant["tenant"] ?? "null"));
    }
    
    /**
     * Encontra tenant baseado no host
     */
    private function findTenant($dominio, $nome_tenant = '')
    {
        global $TENANTS_CONFIG;
        
        if (!isset($TENANTS_CONFIG) || !is_array($TENANTS_CONFIG)) {
            return null;
        }
        
        // Usar função existente se disponível
        if (function_exists('encontra_tenant')) {
            return encontra_tenant($dominio, $nome_tenant);
        }
        
        // Implementação simplificada
        foreach ($TENANTS_CONFIG as $key => $tenant) {
            if (isset($tenant['host']) && $tenant['host'] === $dominio) {
                return $tenant;
            }
        }
        
        return null;
    }
    
    /**
     * Obtém as configurações
     */
    public function getSettings()
    {
        if (!$this->initialized) {
            $this->initialize();
        }
        return $this->settings;
    }
    
    /**
     * Obtém tenant
     */
    public function getTenant()
    {
        if (!$this->initialized) {
            $this->initialize();
        }
        return $this->tenant;
    }
    
    /**
     * Atualiza configurações
     */
    public function updateSettings($key, $value)
    {
        if (!$this->initialized) {
            $this->initialize();
        }
        $this->settings[$key] = $value;
    }
    
    /**
     * Atualiza tenant
     */
    public function updateTenant($tenant)
    {
        if (!$this->initialized) {
            $this->initialize();
        }
        $this->tenant = $tenant;
        $this->settings["tenant"] = $tenant;
    }
    
    /**
     * Força recarregamento das configurações
     */
    public function reload()
    {
        $this->initialized = false;
        $this->initialize();
    }
    
    /**
     * Compatibilidade com variáveis globais antigas
     * Define as variáveis globais para manter compatibilidade
     */
    public function setGlobalVars()
    {
        if (!$this->initialized) {
            $this->initialize();
        }
        
        $GLOBALS['SETTINGS'] = $this->settings;
        $GLOBALS['TENANT'] = $this->tenant;
        
        error_log("ConfigManager Debug: Variáveis globais definidas para compatibilidade");
    }
    
    /**
     * Limpa cache em caso de reset do Swoole
     */
    public static function reset()
    {
        self::$instance = null;
    }
    

    
    /**
     * Detecta mudanças no host para reconfiguração dinâmica
     */
    public function detectHostChange()
    {
        static $lastHost = null;
        
        $currentHost = $_SERVER['HTTP_HOST'] ?? 'localhost';
        $currentHost = preg_replace('/:\d+$/', '', $currentHost);
        
        if ($lastHost !== null && $lastHost !== $currentHost) {
            error_log("ConfigManager Debug: Host mudou de '$lastHost' para '$currentHost', recarregando configurações");
            $this->reload();
        }
        
        $lastHost = $currentHost;
    }
    
    /**
     * Verifica integridade das configurações
     */
    public function validateConfig()
    {
        if (!$this->initialized) {
            return false;
        }
        
        // Verificar se settings básicos existem
        if (!is_array($this->settings) || !isset($this->settings['version'])) {
            error_log("ConfigManager Debug: Configurações corrompidas, recarregando...");
            $this->reload();
            return false;
        }
        
        // Verificar se tenant está válido
        if ($this->tenant && !isset($this->tenant['tenant'])) {
            error_log("ConfigManager Debug: Tenant corrompido, recarregando...");
            $this->reload();
            return false;
        }
        
        return true;
    }
}