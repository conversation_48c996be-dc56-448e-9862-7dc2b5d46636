<?php

/**
 * Wrapper para Twig que mantém compatibilidade com a interface do H2O
 * Permite migração gradual do H2O para Twig
 */

// Carregar autoloader do Composer se não estiver carregado
if (!class_exists('Twig\Environment')) {
    if (file_exists(__DIR__ . "/../vendor/autoload.php")) {
        require_once __DIR__ . "/../vendor/autoload.php";
    } elseif (file_exists("/var/www/html/vendor/autoload.php")) {
        require_once "/var/www/html/vendor/autoload.php";
    }
}

use Twig\Environment;
use Twig\Loader\FilesystemLoader;
use Twig\TwigFilter;

// Incluir funções necessárias
require_once __DIR__ . '/functions.inc.php';

class TwigWrapper {
    private $twig;
    private $variables = [];
    private $template_name;
    
    public function __construct($template_name = null, $options = []) {
        $this->template_name = $template_name;

        // Configurar diretórios de busca (searchpath do H2O)
        $searchPaths = $options['searchpath'] ?? [];

        // Para desenvolvimento, adicionar caminhos conhecidos baseados no diretório atual
        $currentDir = $_SERVER['DOCUMENT_ROOT'] ?? '/var/www/html';

        // Tentar obter o tenant atual do ConfigManager
        $tenantPath = '';
        if (class_exists('ConfigManager')) {
            try {
                $configManager = ConfigManager::getInstance();
                $tenant = $configManager->getTenant();
                if ($tenant && isset($tenant['tenant'])) {
                    $tenantPath = '/var/www/html/tenants/' . $tenant['tenant'] . '/templates';
                }
            } catch (Exception $e) {
                // Fallback silencioso se ConfigManager não estiver disponível
                error_log("TwigWrapper Debug: Não foi possível obter tenant do ConfigManager: " . $e->getMessage());
            }
        }

        $defaultPaths = [
            $currentDir . '/templates',
            '/var/www/html/templates',
            '/home/<USER>/projetos/lettore/editor/templates',
        ];

        // Adicionar caminho do tenant se disponível
        if (!empty($tenantPath)) {
            $defaultPaths[] = $tenantPath;
        }

        $searchPaths = array_merge($searchPaths, $defaultPaths);
        
        // Garantir que os caminhos existam
        $validPaths = [];
        foreach ($searchPaths as $path) {
            if (is_dir($path)) {
                $validPaths[] = $path;
                error_log("TwigWrapper Debug: Caminho válido encontrado: " . $path);
            } else {
                error_log("TwigWrapper Debug: Caminho inválido: " . $path);
            }
        }

        if (empty($validPaths)) {
            $validPaths = ['/var/www/html/templates'];
            error_log("TwigWrapper Debug: Usando caminho padrão: /var/www/html/templates");
        }

        error_log("TwigWrapper Debug: Caminhos finais: " . implode(', ', $validPaths));
        
        // Criar loader do Twig
        $loader = new FilesystemLoader($validPaths);
        
        // Configurar ambiente Twig baseado no ambiente
        $isDevelopment = ($_ENV['APP_ENV'] ?? 'production') === 'development';

        $twigOptions = [
            'cache' => $isDevelopment ? false : '/var/www/html/cache/twig',
            'debug' => $isDevelopment,
            'auto_reload' => $isDevelopment,
            'strict_variables' => $isDevelopment,
        ];

        // Criar diretório de cache se necessário
        if (!$isDevelopment && !is_dir('/var/www/html/cache/twig')) {
            mkdir('/var/www/html/cache/twig', 0755, true);
        }
        
        $this->twig = new Environment($loader, $twigOptions);
        
        // Adicionar filtros customizados para compatibilidade com H2O
        $this->addCustomFilters();
    }
    
    /**
     * Adicionar filtros customizados para manter compatibilidade
     */
    private function addCustomFilters() {
        // Filtro encoda_url (equivalente ao urlencode)
        $this->twig->addFilter(new TwigFilter('encoda_url', function ($string) {
            return urlencode($string);
        }));
        
        // Filtro formataData
        $this->twig->addFilter(new TwigFilter('formataData', function ($data) {
            return date("d/m/Y", strtotime($data));
        }));
        
        // Filtro ConverteData
        $this->twig->addFilter(new TwigFilter('ConverteData', function ($data) {
            $data = explode(' ', $data);
            
            if (strstr($data[0], "/")) {
                $d = explode("/", $data[0]);
                if (count($d) < 3) return '';
                if (!checkdate($d[1], $d[0], $d[2])) return '';
                return "$d[2]-$d[1]-$d[0]";
            } elseif (strstr($data[0], "-")) {
                $d = explode("-", $data[0]);
                if (count($d) < 3) return '';
                if (!checkdate($d[1], $d[2], $d[0])) return '';
                return "$d[2]/$d[1]/$d[0]";
            }
            
            return "";
        }));
        
        // Filtro highlight
        $this->twig->addFilter(new TwigFilter('highlight', function ($string, $words = '') {
            if (function_exists('highlight')) {
                return highlight($string, $words);
            }
            return $string;
        }));
        
        // Filtro base64_encode
        $this->twig->addFilter(new TwigFilter('base64_encode', function ($string) {
            return base64_encode($string);
        }));
        
        // Filtro n2lbr (newline to line break)
        $this->twig->addFilter(new TwigFilter('n2lbr', function ($string) {
            return nl2br($string);
        }));
        
        // Filtro time_elapsed_string (placeholder)
        $this->twig->addFilter(new TwigFilter('time_elapsed_string', function ($datetime) {
            return $datetime; // Implementar se necessário
        }));
        
        // Filtro selected
        $this->twig->addFilter(new TwigFilter('selected', function ($value1, $value2) {
            return ($value1 == $value2) ? 'selected="selected"' : '';
        }));
    }
    
    /**
     * Definir variável no template (compatibilidade com H2O)
     */
    public function set($key, $value) {
        $this->variables[$key] = $value;
    }
    
    /**
     * Renderizar template (compatibilidade com H2O)
     */
    public function render() {
        if (!$this->template_name) {
            throw new Exception("Nome do template não definido");
        }
        
        try {
            return $this->twig->render($this->template_name, $this->variables);
        } catch (Exception $e) {
            throw new Exception("Erro ao renderizar template '{$this->template_name}': " . $e->getMessage());
        }
    }
    
    /**
     * Método estático para parsing de string (compatibilidade com H2O)
     */
    public static function parseString($source, $options = []) {
        // Para templates inline, seria necessário usar StringLoader
        // Por enquanto, retornar erro explicativo
        throw new Exception("parseString não implementado ainda. Use arquivos de template.");
    }
}

/**
 * Função helper para manter compatibilidade com h2o()
 */
function h2o($template_name, $options = []) {
    return new TwigWrapper($template_name, $options);
}

/**
 * Classe para manter compatibilidade com addFilter do H2O
 */
class h2o {
    public static function addFilter($name, $callback = null) {
        // Os filtros já estão implementados no TwigWrapper
        // Esta função existe apenas para compatibilidade
    }
}
