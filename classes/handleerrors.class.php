<?php


class ErrorX {

  # Tipo PHP ou Execução
  public $tipo = 1;
  public $mensagem;
  public $datahora;

  # Informações da requisição.
  public $server = Array();
  public $session = Array();
  public $post = Array();
  public $get = Array();

  # Informações do ERRO do PHP
  public $arquivo = NULL;
  public $linha = NULL;
  public $level = NULL;
  public $context = array();

  # Na construção, já seta algumas informações.
  function __construct($mensagem) {

    $this->tipo = 1;
    $this->mensagem = $mensagem;

    # Data Atual
    $this->datahora = date("Y-m-d H:i:s");

    # Informações da requisição
    $this->server = $_SERVER;
    $this->session = isset($_SESSION) ? $_SESSION : array();
    $this->post = $_POST;
    $this->get = $_GET;

  }

  public function setPHPError($arquivo, $linha, $level) {
    $this->tipo = 0;
    $this->arquivo = $arquivo;
    $this->linha = $linha;
    $this->level = $level;
  }

}

class HandleErrors {

  # Variavel que guarda os erros ocorridos durante a execução do script
  public $PHP_ERRORS = array();

  private $isJson;
  private $Geral;
  private $exit = true;

  # Construtora do Gerenciador de Error
  # Seta as diretivas necessárias.
  function __construct($Geral, $isJson) {

    $this->Geral = $Geral;

    # Inicia o OB
    ob_start();
    # Tratamento de erros no PHP - Erros básicos
    set_error_handler(Array($this, "errorHandler"));
    # Tratamento de erros no PHP - Erros Fatais e Parse
    register_shutdown_function(Array($this, "shutdownHandler"));

    $this->setJsonRetorno($isJson);

  }

  # Retorno em HTML dos erros
  public function errorHTML($url, $paged = true) {

    $list = $this->getErrors();

    # ERRO RETORNARDO POR EMAIL PARA O WEBMASTER
    # ------------------------------------------
    $erro = $list[0];

    # Enviando o email de erro para o webmaster
    $template = $this->Geral->template($url, $this->Geral->settings["template_errors_dir"]);
    $template->set("erro", $erro);


    # Colocando no contexto informacoes do erro
    $arr_erro["get_dump"] = print_r($erro->get, true);
    $arr_erro["post_dump"] = print_r($erro->post, true);
    $arr_erro["server_dump"] = print_r($erro->server, true);
    $arr_erro["session_dump"] = print_r($erro->session, true);
    $arr_erro["context_dump"] = print_r($erro->context, true);

    return $template->render();
  }

  # Retorno em JSON dos er ros.
  public function errorJSON($url) {

    $list = $this->getErrors();
    $html = $this->errorHTML($url, false);

    $retorno = Array(
        "html" => $html,
        "erro" => "-1",
        "mensagem" => "Ocorreu um erro no servidor!",
        "mensagem_erro" => "Ocorreu um erro: ".$list[0]->mensagem." No arquivo ".$list[0]->arquivo." na linha ".$list[0]->linha
    );


    return json_encode($retorno);

  }

  public function setJsonRetorno($isJson) {
    $this->isJson = $isJson;
  }

  public function getErrors() {
    return $this->PHP_ERRORS;
  }

  public function setSendEmail($bool) {
    $this->sendEmail = $bool;
  }

  # Função que lança o erro.
  # Joga no holder e nos arquivos de log.
  public function putError($error) {

    # Stack errors
    $this->PHP_ERRORS[] = $error;

  }

  # Função que captura os erros simples ocorridos na página
  # e lança os erros.
  public function errorHandler($err_level, $err_msg, $err_file, $err_line) {

    # Cria o objeto erro
    $error = new ErrorX($err_msg);

    # Seta como um erro proveniente do php
    $error->setPHPError($err_file, $err_line, $err_level);

    # Adiciona
    $this->putError($error);

    if($this->exit && !extension_loaded('swoole')) exit;

  }

  # Função executa no final do script, quando a página termina e vai responder para o browser
  # Verifico se o fim não foi gerado por nenhum erro e lanço esse erro também.
  public function shutdownHandler() {

    if(count($this->PHP_ERRORS) == 0):
        
        # Tento pegar o último erro.
        $lasterror = error_get_last();

        # Se houver ultimo erro, padronizo e lanço.
        if($lasterror != NULL):
          $this->exit = false;
          $this->errorHandler($lasterror['type'], $lasterror['message'], $lasterror['file'], $lasterror['line'], "");
        endif;

    endif;

    # Finalmente, minha página carregou
    # Vou verifica se algum erro foi lançado.

      # OCORREU ERROS:
    if(count($this->PHP_ERRORS) > 0):

      # Limpo a saida do PHP
      ob_clean();

      # Error templating
      $template_url = "default.html";

      # Capturo o primeiro erro
      $erro = $this->PHP_ERRORS[0];

      if(isset($erro->context["set_code_error_handler"])):

        # Tratamento especifico de erros relacionados a conexão.
        if( $erro->context["set_code_error_handler"] == "connection" ):
            $template_url = "connection.html";
        endif;

        # Tratamento especifico de erros relacionados a conexão.
        if( $erro->context["set_code_error_handler"] == "api" ):
            $template_url = "api.html";
        endif;

        # Tratamento especifico de erros relacionados a conexão.
        if( $erro->context["set_code_error_handler"] == "404" ):
            $template_url = "404.html";
        endif;

        # Tratamento especifico de erros relacionados a conexão.
        if( $erro->context["set_code_error_handler"] == "403" ):
            $template_url = "403.html";
        endif;

        # Tratamento especifico de erros relacionados a conexão.
        if( $erro->context["set_code_error_handler"] == "500" ):
            $template_url = "500.html";
        endif;


      endif;

      # Debug: verificar quando HandleErrors está sendo chamado
      error_log("HandleErrors Debug: Erro sendo processado - isJson = " . ($this->isJson ? 'true' : 'false'));
      error_log("HandleErrors Debug: Número de erros = " . count($this->PHP_ERRORS));
      if (count($this->PHP_ERRORS) > 0) {
          error_log("HandleErrors Debug: Primeiro erro = " . print_r($this->PHP_ERRORS[0], true));
      }

      # Verifico o tipo de erro para retornar.
      if( $this->isJson ):
        error_log("HandleErrors Debug: Retornando JSON");
        print $this->errorJSON($template_url);
      else:
        error_log("HandleErrors Debug: Retornando HTML");
        print $this->errorHTML($template_url);
      endif;

    endif;

  }

}

?>
