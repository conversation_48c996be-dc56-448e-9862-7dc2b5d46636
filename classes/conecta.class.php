<?php

class Query {

  private $resultado;
  private $query_executada = "";

  function __construct($squery, $link) {
    $this->query($squery, $link);
  }

  # Função que executa a query string;
  private function query($squery, $link) {

    $this->query_executada = $squery;

    # Executa a query
    $this->resultado = mysqli_query($link, $squery);

    # Caso não execute, dispara uma exceção.
    if( !$this->resultado ):



      $s = "Não foi possivel a executar a query: ".mysqli_error($link);

      die("\n\n\n\nError:" . $s . "\n\n\n\nQuery: ".$squery);

      exit;
    endif;

    return true;
  }

  public function result() {
    return $this->resultado;
  }

  public function encode_row(&$row) {
    foreach($row as $k => &$v) 
        if($k != "capa" && $k != "imagem") 
          $v = Encoding::toUTF8($v);
    return $row;
  }

  # Função que percorre os registros encontrados e retorna um array.
  public function fetch() {
    $data = array();
    while( $r = mysqli_fetch_assoc($this->resultado) ) $data[] = $this->encode_row($r);

    return $data;
  }  

  # Função que percorre os registros encontrados e retorna um array.
  public function fetchone() {

    $r = mysqli_fetch_assoc($this->resultado);

    if(!$r) return;

    return $this->encode_row( $r );
  }

  # Função que percorre os registros encontrados e retorna um array.
  public function fetchobj() {
    $data = array();
    while( $r = mysqli_fetch_object($this->resultado) ) $data[] = $this->encode_row($r);
    return $data;
  }

  # Retorna o número de linhas encontradas.
  public function rows() {
    if(!$this->resultado) return false;
    return mysqli_num_rows($this->resultado);
  }

}

/**
* Classe para conexão com o banco de dados.
*
* <AUTHOR> da Cruz Bueno
* @version v 1.0 03/06/2013
* @access public
*/
class Conecta {

  # Configurações
  private $host = "";
  private $usuario = "";
  private $password = "";
  private $database = "";
  private $port = 3306;

  # Tenant configuration
  public $tenant = null;

  # Holders
  private $link;
  private $resultado;
  public $trans = false;

  public $autocommit = true;

  /**
  * Construtor da classe
  * Abre uma conexão com o banco de dados.
  */
  function __construct($dblink = NULL) {

    global $TENANT;

    if($TENANT):

      if(isset( $TENANT )):

        $config = $TENANT["mysql"];

        $this->host = $config["host"];
        $this->password = base64_decode($config["pass"]);
        $this->usuario = $config["user"];
        $this->database = $config["base"];
        $this->port = $config["port"];
        $this->tenant = $TENANT;

else:
        trigger_error("Não foi possivel encontrar a tentant.");
      endif;

    endif;

    # Caso já for passado um dblink então setamos ele como o padrão da classe
    # E não permitimos que essa instancia commite as alterações e nem desconecte quando for destruido.
    if($dblink):
      $this->autocommit = false;
      $this->link = $dblink;
    else:
      $this->conectar();
    endif;

  }

  /**
  * Destrutor da classe
  * Fecha a conexão com o banco de dados.
  */
  function __destruct() {
    if($this->autocommit):
      $this->desconectar();
    endif;
  }

  public function getLink() {
    return $this->link;
  }

   /**
   * Conecta comm o banco de dados.
   * @return retorna true se conseguiu conecta com o servidor e com o banco de dados.
   */
  public function conectar() {
	 # Muda o template do erro desde escopo, baseado no context variables.
      $set_code_error_handler = "connection";

      # Efetua a conexão usando configurações da tenant ou ambiente Docker
      if (getenv('DB_HOST')) {
          // Configuração Docker/Ambiente
          $host = getenv('DB_HOST');
          $port = getenv('DB_PORT') ?: 3306;
          $user = getenv('DB_USERNAME');
          $pass = getenv('DB_PASSWORD');
          // Para ambiente Docker, usar banco padrão em vez do banco da tenant
          $database = 'lettore_dev';
          $this->link = mysqli_connect($host, $user, $pass, $database, $port);
      } else {
          // Configuração de produção da tenant
          $this->link = mysqli_connect(
              sprintf("%s:%s", $this->host, $this->port),
              $this->usuario,
              $this->password,
              $this->database
          );
      }

      # Caso não selecione o DB, dispara uma exceção.
      if (!$this->link):
          trigger_error("Não foi possível selecionar o banco de dados ({$this->host}) [2].");
          return false;
      endif;

      # Configurar charset UTF8MB4 (compatível MySQL 8.0)
      mysqli_query($this->link, "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci");
      mysqli_query($this->link, "SET CHARACTER SET utf8mb4");
      mysqli_query($this->link, "SET sql_mode=(SELECT REPLACE(@@sql_mode,'ONLY_FULL_GROUP_BY',''))");

      return true;
  }

  /**
   * Desconecta do o banco de dados.
   * @return retorna true se conseguiu desconecta com o servidor e com o banco de dados.
   */
  public function desconectar() {

      if ($this->link) {
          mysqli_close($this->link);
      }

      return true;
  }

  /**
   * Efetua uma query no banco
   * @param $squery Recebe a query string a ser executada
   * @return retorna um objeto query da string executada.
   */
  public function query($squery) {

      # Verifica a conexão com o banco de dados
      if (!$this->link) {
          return false;
      }

      # Cria o objeto de Query e retorna.
      $query = new Query($squery, $this->link);

      return $query;
  }

  public function transaction() {

      if ($this->trans) {
          return true;
      }

      $query = $this->query("SET autocommit = 0");
      
      $query = $this->query("START TRANSACTION");

      if ($query->result()) {
          $this->trans = true;
      }

      return $query->result();
  }

  public function rollback() {

      $query = $this->query("ROLLBACK");
      
      if ($query->result()) {
          $this->trans = false;
      }

      return $query->result();
  }

  public function commit() {

      if (!$this->autocommit) {
          return true;
      }

      $query = $this->query("COMMIT");
      
      if ($query->result()) {
          $this->trans = false;
      }

      return $query->result();
  }

  public function escape_string($s) {
    // Garantir que a string está em UTF-8 (compatível com utf8mb4)
    $detect = mb_detect_encoding($s, 'auto');
    if ($detect && $detect !== 'UTF-8') {
        $s = mb_convert_encoding($s, 'UTF-8', $detect);
    }

    return mysqli_real_escape_string($this->link, $s);
  }  

  public function escape_date($s) {
    if(strlen($s) == 0) return 'NULL';
    else return "'".$s."'";
  }

  public function escape_float($s) {
    if (!$s) return 0;
    return $s;
  }


}

?>
