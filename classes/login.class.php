<?php

require_once("conecta.class.php");

class Login extends Conecta {

  public $usuario = null;
  public $senha = null;

  function __construct() {

    parent::__construct();
  }

  function __destruct() {
    parent::__destruct();
  }

  public function setUsuario($usuario) {
    $this->usuario = $usuario;
  }

  public function setSenha($senha) {
    $this->senha = sha1($senha);
  }

  public function getUsuario($usuario) {
    $this->usuario = $usuario;
  }

  public function getSenha($senha) {
    $this->senha = $senha;
  }


  public function login(&$usuario = null) {
    error_log("Login.class Debug: Iniciando login para usuario: " . $this->usuario);
    error_log("Login.class Debug: Senha hash: " . $this->senha);
    
    # Busca o usuário no banco de dados
    $query = $this->query(sprintf("SELECT * FROM usuarios WHERE (usuario like '%s') AND ativo = 1", $this->usuario));
    
    error_log("Login.class Debug: Query executada: SELECT * FROM usuarios WHERE (usuario like '" . $this->usuario . "') AND ativo = 1");
    
    if(!$query) {
        error_log("Login.class Debug: Query falhou");
        return -1;
    }

    # Verifica se teve 1 registro encontrado
    $rows = $query->rows();
    error_log("Login.class Debug: Numero de registros encontrados: " . $rows);
    
    if(!$rows) {
        error_log("Login.class Debug: Nenhum usuario encontrado");
        return -2;
    }

    # Pega o primeiro registro encontrado.
    $fetch = $query->fetch();
    $fetch = $fetch[0];

    error_log("Login.class Debug: Usuario encontrado no banco: " . print_r($fetch, true));
    error_log("Login.class Debug: Senha no banco: " . $fetch["senha"]);
    error_log("Login.class Debug: Senha informada (hash): " . $this->senha);

    $usuario = $fetch;

    $_SESSION["ULTIMO_USUARIO_AUTENTICADO"] = $fetch;

    # Compara o usuário e a senha passada, com o registro encontradoa através do login
    if( $fetch["senha"] == $this->senha ):
      error_log("Login.class Debug: Senhas coincidem - autenticação OK");
      $usuario = $fetch;
      return 0;
    endif;

    error_log("Login.class Debug: Senhas não coincidem - autenticação FALHOU");
    return -3;

  }

}

?>
