<?php

/**
 * Classe para gerenciar cache usando Redis
 */
class RedisCache {

    private static $instance = null;
    private $redis = null;
    private $prefix = 'lettore_cache:';

    private function __construct() {
        try {
            $this->redis = new Redis();
            $this->redis->connect('lettore-redis', 6379);
        } catch (Exception $e) {
            error_log("RedisCache: Erro ao conectar no Redis: " . $e->getMessage());
            $this->redis = null;
        }
    }

    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Salva dados no cache
     * @param string $key Chave do cache
     * @param mixed $data Dados a serem salvos
     * @param int $ttl Tempo de vida em segundos (padrão: 1 hora)
     * @return bool
     */
    public function set($key, $data, $ttl = 3600) {
        if ($this->redis === null) {
            error_log("RedisCache: Redis não disponível para set");
            return false;
        }

        try {
            $value = json_encode($data);
            return $this->redis->setex($this->prefix . $key, $ttl, $value);
        } catch (Exception $e) {
            error_log("RedisCache: Erro ao salvar no cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Recupera dados do cache
     * @param string $key Chave do cache
     * @return mixed|null Retorna os dados ou null se não encontrado
     */
    public function get($key) {
        if ($this->redis === null) {
            error_log("RedisCache: Redis não disponível para get");
            return null;
        }

        try {
            $value = $this->redis->get($this->prefix . $key);
            if ($value === false) {
                return null;
            }
            return json_decode($value, true);
        } catch (Exception $e) {
            error_log("RedisCache: Erro ao buscar do cache: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Remove uma chave do cache
     * @param string $key Chave do cache
     * @return bool
     */
    public function delete($key) {
        if ($this->redis === null) {
            return false;
        }

        try {
            return $this->redis->del($this->prefix . $key) > 0;
        } catch (Exception $e) {
            error_log("RedisCache: Erro ao deletar do cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Limpa todo o cache
     * @return bool
     */
    public function flush() {
        if ($this->redis === null) {
            return false;
        }

        try {
            $keys = $this->redis->keys($this->prefix . '*');
            if (empty($keys)) {
                return true;
            }
            return $this->redis->del($keys) > 0;
        } catch (Exception $e) {
            error_log("RedisCache: Erro ao limpar cache: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Verifica se o Redis está disponível
     * @return bool
     */
    public function isAvailable() {
        return $this->redis !== null;
    }
}
