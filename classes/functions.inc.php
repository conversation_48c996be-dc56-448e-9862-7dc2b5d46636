<?php



function verifica_processo_montagem() {

  // Verificar se exec() está disponível
  if (!function_exists('exec')) {
      // Se exec() não está disponível, retornar array com todos false
      return array(
          "etapa_indexar" => false,
          "etapa_tratar" => false,
          "etapa_mastigar" => false
      );
  }

  try {
      \exec("ps auxww", $output);
  } catch (\Exception $e) {
      // Em caso de erro, retornar array com todos false
      return array(
          "etapa_indexar" => false,
          "etapa_tratar" => false,
          "etapa_mastigar" => false
      );
  }


  $arr = array("etapa_indexar", "etapa_tratar", "etapa_mastigar");


  $retorno = array();

  # Zerando o array
  foreach($arr as $metodo) $retorno[$metodo] = false;

  # Percorro cada linha, e verifico se o sincronizar está rodando
  foreach($output as $linha) {
      foreach($arr as $metodo) {

          if( strpos($linha, $metodo) > 0)
              $retorno[$metodo] = true;

      }
  }

  return $retorno;

}


function gerar_jbe($tenant, $idobra) {

      $url = sprintf("http://gerador-jbe.lettore.com.br/jbe?idobra=%d&editora=%s", $idobra, $tenant);

      $dirname = ROOT_SYS."/obras_montadas/".$tenant."/";

      $filename = sprintf("%s%d.jbe", $dirname, $idobra);

      if(!file_exists($dirname)) mkdir($dirname, 0777, true);

      $fp = fopen($filename, 'w+');

      $ch = curl_init($url);
      curl_setopt($ch, CURLOPT_TIMEOUT, 0);
      curl_setopt($ch, CURLOPT_FILE, $fp);
      curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
      curl_exec($ch);
      curl_close($ch);

      fclose($fp);

      # Setando permissao
      if(file_exists($filename)){
	      chmod( $filename, 0666);
      }
}

function verifica_imagem_upload($arquivo, $config) {

    if(!preg_match("/image\/(pjpeg|jpeg|png|gif|bmp)/", $arquivo["type"]))
      return array(-1, "Tipo do arquivo inválido. O capa deve ser uma imagem.");

    $tamanhos = getimagesize($arquivo["tmp_name"]);

    if($tamanhos[0] > $config["maxw"])
      return array(-1, "Imagem muito larga.");

    if($tamanhos[1] > $config["maxh"])
      return array(-1, "Imagem muito alta.");

    if($tamanhos[0] < $config["minw"])
      return array(-1, "Imagem muito estreita.");

    if($tamanhos[1] > $config["minh"])
      return array(-1, "Imagem muito alta.");

    return false;

}


# Funcoes Globais
function getIp()
{
    if (!empty($_SERVER['REMOTE_ADDR']))
        $ip = $_SERVER['REMOTE_ADDR'];
    elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR']))
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    else
        $ip = $_SERVER['HTTP_CLIENT_IP'];

    return $ip;
}

function limpa_texto($texto) {

  # Removendo os acentos
  $trocarIsso = array('à','á','â','ã','ä','å','ç','è','é','ê','ë','ì','í','î','ï','ñ','ò','ó','ô','õ','ö','ù','ü','ú','ÿ','À','Á','Â','Ã','Ä','Å','Ç','È','É','Ê','Ë','Ì','Í','Î','Ï','Ñ','Ò','Ó','Ô','Õ','Ö','O','Ù','Ü','Ú','Ÿ',);
  $porIsso = array('a','a','a','a','a','a','c','e','e','e','e','i','i','i','i','n','o','o','o','o','o','u','u','u','y','A','A','A','A','A','A','C','E','E','E','E','I','I','I','I','N','O','O','O','O','O','O','U','U','U','Y',);
  $titletext = str_replace($trocarIsso, $porIsso, $texto);

  # colocando caixa-baixa
  return strtolower($titletext);
}

function smart_pagination($data, $limit = null, $current = null, $adjacents = null) {

    $result = array();
    if (isset($data, $limit) === true) {

        $result = range(1, ceil($data / $limit));
        if (isset($current, $adjacents) === true) {
            if (($adjacents = floor($adjacents / 2) * 2 + 1) >= 1) {
                $result = array_slice($result, max(0, min(count($result) - $adjacents, intval($current) - ceil($adjacents / 2))), $adjacents);
            }
        }
    }

    return $result;
}

function slugify($text) {

   // replace non letter or digits by -
   $text = preg_replace('~[^\\pL\d]+~u', '-', $text);

   // trim
   $text = trim($text, '-');

   // transliterate
   $text = iconv('utf-8', 'us-ascii//TRANSLIT', $text);

   // lowercase
   $text = strtolower($text);

   // remove unwanted characters
   $text = preg_replace('~[^-\w]+~', '', $text);

   if (empty($text))
   {
     return 'n-a';
   }

   return $text;
  }

  function retornaPaginacao($pagina, $quantidade, $total = NULL) {

    $p = array();

    # Verifica se são válidas
    if( $pagina < 1 ) $pagina = 1;

    # Registro Inicial e Final
    $registro_inicial = ($pagina-1) * $quantidade;

    if($total != NULL):

      $total_paginas = ceil( $total / $quantidade);
      if( $pagina > $total_paginas) $pagina = $total_paginas;

      $registro_final = $registro_inicial + $quantidade;
      if($registro_final > $total ) $registro_final = $total;

      $p["registro_final"] = $registro_final;
      $p["registro_total"] = $total;
      $p["pagina_total"] = $total_paginas;

    endif;

    # Retorno
    $p["pagina_quantidade"] = $quantidade;
    $p["pagina_atual"] = $pagina;
    $p["registro_inicial"] = $registro_inicial + 1;

    return $p;

  }

  function time_elapsed_string($datetime, $full = false) {
    $now = new DateTime;
    $ago = new DateTime($datetime);
    $diff = $now->diff($ago);

    $diff->w = floor($diff->d / 7);
    $diff->d -= $diff->w * 7;

    $string = array(
        'y' => array('ano', 'anos'),
        'm' => array('mês', 'meses'),
        'w' => array('semana', 'semanas'),
        'd' => array('dia', 'dias'),
        'h' => array('hora', 'horas'),
        'i' => array('minuto', 'minutos'),
        's' => array('segundo', 'segundos')
    );
    foreach ($string as $k => &$v) {

        $sing = $v[0]; $plur = $v[1];

        if ($diff->$k) {
            $v = $diff->$k . ' ' . ($diff->$k > 1 ? $v[1] : $v[0]);
        } else {
            unset($string[$k]);
        }

    }

    if (!$full) $string = array_slice($string, 0, 1);
    return $string ? implode(', ', $string) . ' atrás' : 'agora';
}


function validaEmail($email) {
    $conta = "^[a-zA-Z0-9\._-]+@";
    $domino = "[a-zA-Z0-9\._-]+.";
    $extensao = "([a-zA-Z]{2,4})$";
    $pattern = $conta.$domino.$extensao;
    if (ereg($pattern, $email))
    return true;
    else
    return false;
}

function moeda2float($str) {
    return str_replace(",", ".", str_replace(".", "", $str) );
}

function float2moeda($num) {
    return number_format($num, 2, ',', '.');
}





        function formata_coordenadas_palavra($arr, $arr_pagina, $px, $py, $des_left, $des_top) {

          # Convertendo todo mundo para pixels
          $left = (($arr["x1"] - $arr_pagina["x1"]) * $px) - $des_left;
          $top = (( $arr_pagina["y2"] - $arr["y2"]) * $py) - $des_top;
          $width = ($arr["x2"] - $arr["x1"]) * $px;
          $height = ($arr["y2"] - $arr["y1"]) * $py;

          # Retorna formatdo
          return array("left" => $left, "top" => $top, "width" => $width, "height" => $height);
        }

        function formata_coordenadas_pagina($arr, $px, $py) {

          $width = ($arr["x2"] - $arr["x1"]) * $px;
          $height = ($arr["y2"] - $arr["y1"]) * $py;

          # Retorna formatdo
          return array("left" => 0, "top" => 0, "width" => $width, "height" => $height);
        }

  function save_ini($ini, $ini_obj, $pagina = 0, $status = "not started", $log = "") {

        $ini_obj["pagina"] = $pagina;
        $ini_obj["status"] = $status;

        if(!isset($ini_obj["log"])) array();
        $ini_obj["log"][] = sprintf("%s - %s ", date("d/m/Y H:i:s"), $log);

#	print '['.date('d/m/Y H:i:s').'] '.$log;
        file_put_contents($ini, json_encode($ini_obj) );

        return $ini_obj;
    }

  function get_ini($ini) {

        if(!file_exists($ini)) return false;

        $content = file_get_contents($ini);
        $obj = json_decode($content, true);
        return $obj;

  }

  function get_property_regex($prop, $linha) {

        preg_match("/\"".$prop."\":\"([^\"]*)\"/", $linha, $matches);

        if (!isset($matches[1])) return false;

        return $matches[1];

  }

  function replace_property_regex($prop, $value, $linha) {
      return preg_replace("/\"".$prop."\":\"([^\"]*)\"/", "\"".$prop."\":\"".$value."\"", $linha);
  }

  function juntando_palavras_proximas($linhas) {

    $fixeds = 0;
    $fixed_linhas = [];

    for($i=0; $i < count($linhas); $i++ ):

      $current = $linhas[$i];

      # Tem que ter proxima, tem que existir, tem que ter a propriedade txt
      if( isset($linhas[$i+1]) && $current && (get_property_regex("txt", $current) === false)  ):

        $next = trim($linhas[$i+1]);

        if( $next && (get_property_regex("x1", $next) - get_property_regex("x2", $current) < 1.5)  &&
                  (get_property_regex("y1", $next) - get_property_regex("y1", $current) < 2) &&

                  (get_property_regex("y1", $next) - get_property_regex("y1", $current) > -2) ):

          # tem proximo e o proximo encaixa nele.

          $novo_texto = get_property_regex("txt", $current).get_property_regex("txt", $next);

          # Concatenando as palavras
          $current = replace_property_regex("txt", $novo_texto ,$current);
          # Substituindo os valres
          $current = replace_property_regex("y2", get_property_regex("y2", $next) ,$current);
          $current = replace_property_regex("x2", get_property_regex("x2", $next) ,$current);


          $fixed_linhas[] = $current;
          $fixeds++;

          # Ignoro a próxima da brincadeira;
          $i++;

        else:
          // O proximo nao encaixa nele, entao é isso ai
          $fixed_linhas[] = $current;
        endif;

      else:
        // Não tem proximo, entao é isso ai.
        $fixed_linhas[] = $current;
      endif;

    endfor;

    if($fixeds > 0) $fixed_linhas = juntando_palavras_proximas($fixed_linhas);

    return $fixed_linhas;

  }

    function quebra_pesquisa($string)
  {

      $word = $quotes = array();
      $string = strtolower($string);

      // Pega as palavras dentro de aspas
      preg_match_all('/\"([^\"]*)\"/',$string,$_quotes);

      // Retiras essas palavras da string
      foreach ( $_quotes[0] as &$e )
          if (trim($e) != '') $string = str_replace($e,'',$string);

      foreach ( $_quotes[1] as &$e)
          $quotes[] = $e;

      // Pega as outras palavras
      $words = explode(' ',$string);

      $all = array_merge($quotes,$words);

      $array_proibido = array();

      $retorno = array();
      foreach($all as $e)
      {
          $e = trim($e);

          if (!in_array(limpa_texto($e),$array_proibido) && !empty($e) && strlen($e) >= 1)
          {
              $retorno[] = limpa_texto($e);
          }
      }

      return $retorno;
  }

  function highlight_formata_pattern($str) {

    $str = mb_strtolower($str);

    $replacements = array('[a|á|â|à|ã|ä]',
                      '[e|é|ê|è|ë]',
                      '[i|í|î|ì|ï]',
                      '[o|ó|ô|ò|ø|õ|ö]',
                      '[u|ú|û|ù|ü]',
                      '[c|ç]'
                     );

    $patterns = array("/a/",
                      "/e/",
                      "/i/",
                      "/o/",
                      "/u/",
                      "/c/"
                    );

    $str = preg_replace($patterns, $replacements, $str);

    return "!($str)!ui";

  }



  function highlight($text, $words) {

      if(!strlen($words)) return $text;

      # Limpo a busca
      $words = quebra_pesquisa($words);

      # Remove os espacos em branco
      $words = array_filter($words, function($value) {
        return !empty($value) || $value === 0;
      });

      foreach($words as &$word)
        $word = highlight_formata_pattern($word);

      $text = preg_replace($words, "<em>$1</em>", $text);


      return $text;
  }



?>
