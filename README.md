# Sistema de Gestão de E-books

## Sobre o Projeto
Este é um sistema web desenvolvido em PHP para gerenciamento e processamento de e-books. O sistema permite o upload, processamento e gerenciamento de obras digitais, incluindo funcionalidades para sumário, marcações e processamento de PDF.

## Funcionalidades Principais

### 1. Gestão de Obras
- Upload de PDFs
- Cadastro de informações básicas (título, autor, ISBN, etc.)
- Controle de status (rascunho, publicado, em venda)
- Visualização em lista com informações detalhadas

### 2. Processamento de Obras
- Conversão de PDF para formato processável
- Sistema de "mastigação" de conteúdo
- Geração de sumário automático
- Processamento de marcações e grifos
- Integração com ChatGPT para processamento de conteúdo

### 3. Gerenciamento de Conteúdo
- Editor de sumário
- Sistema de marcações
- Controle de níveis de conteúdo
- Gestão de páginas individuais

### 4. Recursos Técnicos
- Sistema multi-tenant
- Gestão de uploads por tenant
- Processamento em background
- Sistema de compactação/descompactação de obras
- Controle de versões e revisões

## Estrutura do Projeto

### Diretórios Principais
- `/views`: Controladores do sistema
- `/templates`: Templates HTML usando sistema de templates
- `/media`: Arquivos estáticos (JS, CSS)
- `/docs`: Documentação e schemas
- `/vendor`: Dependências (via Composer)

### Rotas Principais
- `/obras`: Listagem de obras
- `/obra/cadastro`: Cadastro de novas obras
- `/obra/mastigar`: Processamento de obras
- `/obra/sumario`: Gestão de sumário
- `/obra/publicar`: Publicação de obras

## Requisitos Técnicos
- PHP 7+
- MySQL/MariaDB
- Composer para gerenciamento de dependências
- Servidor web (Apache/Nginx)
- Suporte a processamento de PDF

## Dependências Principais
- AWS SDK para PHP
- Sistema de templates
- Bibliotecas de processamento de PDF

## Configuração do Ambiente

### Ambiente Docker (Recomendado)
1. Clone o repositório
2. Execute `docker-compose up -d`
3. Acesse a aplicação em http://localhost:8080
4. Use as credenciais configuradas no banco de dados

### Ambiente Tradicional
1. Clone o repositório
2. Execute `composer install`
3. Configure o banco de dados usando `docs/schema_montagem.sql`
4. Configure as variáveis de ambiente
5. Configure os diretórios de upload

### Configuração Swoole
O sistema usa Swoole como servidor HTTP para melhor performance:
- **Reiniciar após mudanças**: `docker compose restart lettore-app`
- **Logs do Swoole**: `docker logs lettore-app`
- **Health Check**: http://localhost:8080/health

## Fluxo de Processamento de Obras
1. Upload do PDF
2. Processamento inicial ("mastigação")
3. Geração de sumário
4. Processamento de marcações
5. Revisão e ajustes
6. Publicação

## Manutenção
- Backup regular do banco de dados
- Monitoramento do processamento de obras
- Gestão de espaço em disco para uploads
- Monitoramento de logs de erro

## Segurança
- Autenticação obrigatória
- Controle de acesso por tenant
- Validação de uploads
- Sanitização de dados

## Troubleshooting

### Erro: "Impossible to access an attribute (\"nome\") on a null variable"
**Problema**: Variável do usuário não está sendo passada para o template após o login.

**Soluções**:
1. Reinicie o servidor Swoole: `docker compose restart lettore-app`
2. Verifique os logs: `docker logs lettore-app`
3. Confirme que o usuário existe no banco de dados
4. Verifique se a sessão está funcionando corretamente

### Comandos de Debug
```bash
# Reiniciar aplicação após mudanças no código
docker compose restart lettore-app

# Ver logs da aplicação
docker logs -f lettore-app

# Verificar status dos containers
docker compose ps

# Acessar container da aplicação
docker exec -it lettore-app bash

# Verificar conectividade com Redis
docker exec -it lettore-redis redis-cli ping

# Verificar conectividade com MySQL
docker exec -it lettore-db mysql -u lettore_user -plettore_password lettore_dev
```

### URLs de Teste
- **URL Base**: http://editor.jurid.lettore.com.br:8080
- **Health Check**: http://editor.jurid.lettore.com.br:8080/health
- **Login**: http://editor.jurid.lettore.com.br:8080/

### Arquitetura

#### Componentes
- **Swoole**: Servidor HTTP assíncrono
- **MySQL 8.0**: Banco de dados principal
- **Redis**: Cache e sessões
- **Nginx**: Proxy reverso
- **Twig**: Sistema de templates (migração do H2O)

#### Estrutura de Arquivos
- `/classes/`: Classes PHP principais
- `/templates/`: Templates Twig/HTML
- `/views/`: Controladores (rotas)
- `/media/`: Arquivos estáticos
- `/docker/`: Configurações Docker
- `/uploads/`: Arquivos enviados pelos usuários
- `/tenants/`: Configurações multi-tenant

## Suporte
Para questões técnicas ou suporte, entre em contato com a equipe de desenvolvimento.