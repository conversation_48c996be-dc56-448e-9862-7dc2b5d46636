<!DOCTYPE html>
<html lang="pt-br">
    
    <head>

        <title>
            {% block page_title %}{% endblock %}
        </title>

        <!-- Metas -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <!-- Robots -->
        <meta name="rating" content="General">
        <meta name="robots" content="follow" />
        <meta name="revisit-after" content="1" />

        <!-- SEO - Keywords -->
        <meta name="description" content="{% block page_description %}{% endblock %}">
        <meta name="keywords" content="{% block page_keywords %}{% endblock %}">

        <!-- Favicon -->
        {% block meta_favicon %}
            <link rel="shortcut icon" href="{{media_dir}}/img/favico.png"> 
        {% endblock %}

        <!-- CSS core: Bootstrap, Datepickers, Plugins e etc -->
        {% block core_css %}
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/core_css/bootstrap.min.css" />
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/core_css/font-awesome.css" />
        {% endblock %}

        <style>

            body { text-align: center; color: #fff;  font-family: 'Source Sans Pro', sans-serif;   }
            .img-error {  text-align: center; margin-top: 80px; }


            main { height: 100vh; position: relative; padding-bottom: 30px; padding-top: 30px;}
            h1 { margin-top: 50px; margin-bottom: 0; font-size: 45px; font-weight: 500; }

            p.subtitle { font-weight: 300; font-size: 18px; margin-bottom: 30px; margin-top: 10px; opacity: 0.7;}
            p.suggestion { width: 50%; font-weight: 300; font-size: 14px; margin-bottom: 30px;  margin: 0 auto; opacity: 0.8;}

            a { color:{{tenant.style.text_color}}; text-decoration: none; cursor: pointer; text-decoration: underline; }
            a:hover { color:{{tenant.style.text_color}}; opacity: 0.5; text-decoration: underline;  }

            ul { display: inline-block; margin-top: 10px; opacity: 0.8; color: {{tenant.style.text_color}}}

            footer { height: 30px; width: 100%;  position: absolute; bottom: 0; font-size: 12px;}

            .center-line {
               width: 100%; 
               text-align: center; 
               border-bottom: 1px solid rgba(255,255,255, 0.3); 
               line-height: 0.1em;
            }

            .center-line span { 
                padding:0 10px; 
            }

            @media screen and (max-width: 768px) {
               h1 { margin-top: 0px; font-size: 40px; }
               p.subtitle { font-size: 15px; }
               p.suggestion { width: 80%; }  
            }

        </style>

        <!-- CSS de cores da tenant -->
        <link rel="stylesheet" type="text/css" href="{{media_dir}}/generate_css/{{tenant.tenant}}.css" />


    </head>
    
    <body class="bg-pc">

    <main>
        
                <div class="container">

                    <figure class="text-center img-error hidden-xs">
                        <img width="160px" src="{{media_dir}}/img/icon-404.png" class="inverted"/>
                    </figure>

                    {% block main %}
                    {% endblock %}

                </div>

                <footer>
                    <div class="container">
                        <div class="center-line"><span class="bg-pc text-base"> <strong>{{tenant.nome}}</strong> - Todos os direitos reservados</span></div>
                    </div>
                </footer>
        
    </main>

    {% block core_javascript %}
        <script type="text/javascript" src="{{media_dir}}/core_js/jquery.min.js"></script>
    {% endblock %}

</body>
</html>
