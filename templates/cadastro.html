{% extends 'bases/base.html' %}

{% block javascript %}
	<script type="text/javascript" src="/media/core_js/bootstrap-notify.min.js"></script>
	<script type="text/javascript" src="/media/core_js/bootstrap-toggle.min.js"></script>
    <script>
    $(document).ready(function() {
        // Aguardar elementos aparecerem no DOM
        setTimeout(function() {
            initializeSelect2();
        }, 500);
        
        // Inicializar eventos do formulário
        initializeFormEvents();
    });

    function initializeSelect2() {
        // Verificar se Select2 está disponível
        if (typeof $.fn.select2 === 'undefined') {
            setTimeout(initializeSelect2, 500);
            return;
        }

        $('.select-dois').each(function() {
            const $element = $(this);
            
            // Verificar se tem opções
            if ($element.find('option').length === 0) {
                return;
            }

            // Destruir Select2 existente se houver
            if ($element.hasClass('select2-hidden-accessible')) {
                $element.select2('destroy');
            }

            // Configuração para Select2 v4.0.13
            const config = {
                placeholder: "Selecione...",
                width: '100%',
                allowClear: !$element.prop('multiple'),
                language: {
                    noResults: function() {
                        return "Nenhum resultado encontrado";
                    }
                }
            };

            // Aplicar Select2
            $element.select2(config);
        });
    }

    function initializeFormEvents() {
        // Remover eventos existentes para evitar duplicação
        $('form[name="adicionar-obra"]').off('submit');

        $('form[name="adicionar-obra"]').submit(function(event) {
			if($("#categorias").val() == null) {
                $.notify({
                    message: "&Eacute; necess&aacute;rio selecionar pelo menos uma CATEGORIA.",
                    icon: 'fa fa-exclamation-triangle',
                },{
                    type: 'danger',
                    z_index: 2000
                });
				return false;
			}
            event.preventDefault();
            $(".btn-salvar").button("loading");

            var options = {
                            "dataType":"json",
                            "method": "post",
                            "type": "json",
                            "url": $(this).attr("action"),
                            "success": function(data) {

                                $(".btn-salvar").button("reset");

                                if(data.erro == 0) window.location = "/";
                                else {
                                     $("body").scrollTop(0);
                                    $(".alert").html(data.mensagem).removeClass("hide");

                                }

                            }

                          };

           $(this).ajaxSubmit(options);

            return false;

        });
    }
    </script>

{% endblock %}

{% block main %}

    {% if obra %}
        <h4> <i class="text-pc fa fa-book"></i> Alterando e-book: {{obra.titulo}}</h4>
    {% else %}
        <h4> <i class="text-pc fa fa-book"></i> Cadastrando e-book </h4>
    {% endif %}



    <form role="form" method="post" name="adicionar-obra" enctype="multipart/form-data" action="{{work_dir}}/obra/cadastro{% if obra %}/{{obra.id}}{% endif %}">


    <br/>

                        <div class="alert alert-danger hide" role="alert"></div>


        <input type="hidden" value="salvar" name="action">
        <input type="hidden" value="{% if obra %}{{obra.id}}{% endif %}" name="idobra">

                <h4> Informações do e-book </h4>

                <div class="row row-margin">

                    <div class="col-md-12">
                        Titulo: <br/>
                        <input type="text" class="form-control" value='{% if obra %}{{obra.titulo}}{% endif %}' name="titulo">
                    </div>

                    <div class="col-md-6 col-sm-6">
                        Categoria(s): <br/>
                        <select name="idcategorias[]" data-campo="categorias" id="categorias" class="form-control select-width select-dois obrigatorio" multiple="multiple">
                                {% for categoria in categorias %}
                                    <option value="{{categoria.id}}" {% if categoria.selected == "1" %} selected {% endif %}> {{categoria.categoria}}</option>
                                {% endfor %}

                        </select>
                    </div>

                    <div class="col-md-3 col-sm-3">
                        Ano: <br/>
                        <input type="text" class="form-control" value="{% if obra %}{{obra.ano}}{% endif %}" name="ano">
                    </div>

                    <div class="col-md-3 col-sm-3">
                        Edição: <br/>
                        <input type="text" class="form-control" value="{% if obra %}{{obra.edicao}}{% endif %}" name="edicao">
                    </div>

                   <div class="col-md-4 col-sm-4">
                        Código Editora: <br/>
                        <input type="text" class="form-control" value="{% if obra %}{{obra.codigo_editora}}{% endif %}" name="codigo_editora">
                    </div>

                    <div class="col-md-4 col-sm-4">
                        ISBN: <br/>
                        <input type="text" class="form-control" value="{% if obra %}{{obra.ISBN}}{% endif %}" name="ISBN">
                    </div>

                    <div class="col-md-4 col-sm-4">
                        Data Lançamento: <br/>
                        <input type="text" class="form-control formataData date-mask" value="{% if obra %}{{obra.datalancamento|ConverteData}}{% endif %}" name="datalancamento">
                    </div>

                    <div class="col-md-12">
                        Resumo: <br/>
                        <textarea class="form-control" rows="6" name="dados_obra">{% if obra %}{{obra.dados_obra}}{% endif %}</textarea>
                    </div>

                </div>

                <h4> Informações do autor </h4>

                <div class="row row-margin">

                    <div class="col-md-12">
                        Autor Principal: <br/>
                        <input type="text" class="form-control" value="{% if obra %}{{obra.autor}}{% endif %}" name="autor">
                    </div>
                    <div class="col-md-12">
						Autores: <br/>
                        <select class="form-control select-width select-dois obrigatorio" id="autores" data-campo="autores" name="idautores[]" multiple="multiple">
						{% for autordb in autores_db %}
                               <option value="{{autordb.id}}" {% if autordb.selected == "1" %} selected {% endif %}> {{autordb.nome}}</option>
						{% endfor %}
						</select>
                    </div>
                    <div class="col-md-12">
                        Dados do Autor: <br/>
                        <textarea class="form-control" rows="8" name="dados_autor">{% if obra %}{{obra.dados_autor}}{% endif %}</textarea>
                    </div>
                </div>


                <h4> Disponibilização  </h4>

                <div class="row row-margin">

                        {% if obra %}
                        <div class="col-md-2 col-sm-2">
                            Status: <br/>
                            <select name="status" class="form-control">
                                <option value="0" {{selected_0}}>Rascunho</option>
                                <option value="1" {{selected_1}}>Publicada</option>
                                <option value="2" {{selected_2}}>Publicada p/ Venda</option>
                            </select>
                        </div>
                        {% endif %}

                        <div class="col-md-3 col-sm-3">
                            Preço: <br/>
                            <input type="text" class="form-control" value="{% if obra %}{{obra.preco}}{% endif %}" name="preco">
                        </div>

                        <div class="col-md-2 col-sm-2">
                            Assinatura: <br/>
                            <select name="assinatura" class="form-control">
                                <option value="0" {{selected_assinatura_0}}>Não</option>
                                <option value="1" {{selected_assinatura_1}}>Sim</option>
                            </select>
                        </div>

                        <div class="col-md-3 col-sm-3">
                            Porcentagem do Autor: <br/>
                            <input type="text" class="form-control" value="{% if obra %}{{obra.porcentagemautor}}{% endif %}" name="porcentagemautor">
                        </div>

                </div>

                <h4> Conversão  </h4>

                <div class="row row-margin">

                    <div class="col-md-3">
                        Capa: <br/>
                        <input type="file" class="form-control" name="capa">
                    </div>

                    <div class="col-md-3">
                        Capa Alta Resolução: <br/>
                        <input type="file" class="form-control" name="capa_hi_res">
                    </div>

                    <div class="col-md-3">
                        PDF completo do e-book: <br/>
                        <input type="file" class="form-control" name="pdf">
                    </div>


                    <div class="col-md-3">
                        Ajuste Vertical (px): <br/>
                        <input type="text" class="form-control" name="ajuste_vertical" value="0">
                    </div>

		    <div class="col-md-3">
			Sumário Início
			<input type="text" class="form-control" name="sumario_inicio" value="{% if obra %}{{obra.sumario_inicio}}{% endif %}">
		    </div>

		    <div class="col-md-3">
			Sumário Término
			<input type="text" class="form-control" name="sumario_fim" value="{% if obra %}{{obra.sumario_fim}}{% endif %}">
		    </div>
		    <div class="col-md-6"><br>
			<span>Marque o início e término das páginas que contém o sumário, se o sumário for página única, página término deve ser igual página início.</span>
		    </div>
                </div>

				<div class="row row-margin">
					<div class="col-md-4">
						<input type="checkbox" class="" id="usar_marcadaguacripto" name="usar_marcadaguacripto" value="1" {{ usarmarcadaguacripto }}>
						<label class="custom-control-label" for="usar_marcadaguacripto">Aplicar marca d'&aacute;gua e seguran&ccedil;a no arquivo PDF</span>
					</div>
				</div>

				<div class="row row-margin">
					<div class="col-md-12">
						<input type="checkbox" class="" id="mostrarFaixaLancamento" name="mostrarFaixaLancamento" value="1" {{ opcoes_faixa.faixalancamento }}>
						<label class="custom-control-label" for="mostrarFaixaLancamento">Mostrar faixa "Lan&ccedil;amento" (por 30 dias ap&oacute;s a data de publica&ccedil;&atilde;o)</span>
					</div>
					<div class="col-md-12">
						<input type="checkbox" class="" id="mostrarFaixaAtualizada" name="mostrarFaixaAtualizada" value="1" {{ opcoes_faixa.faixaatualizada }}>
						<label class="custom-control-label" for="mostrarFaixaAtualizada">Mostrar faixa "Atualizada" (por 30 dias ap&oacute;s a data da &uacute;ltima modifica&ccedil;&atilde;o)</span>
					</div>
					<div class="col-md-12">
						<input type="checkbox" class="" id="mostrarFaixaAtualizadaPrimeiro" name="mostrarFaixaAtualizadaPrimeiro" value="1" {{ opcoes_faixa.faixaatualizadaprimeiro }}>
						<label class="custom-control-label" for="mostrarFaixaAtualizadaPrimeiro">Mostrar faixa "Atualizada" mesmo quando a obra ainda for considerada Lan&ccedil;amento (por 30 dias ap&oacute;s a data da &uacute;ltima modifica&ccedil;&atilde;o)</span>
					</div>
				</div>

                <div class="row hide">
                    <div class="col-md-3 col-sm-3 hide">
                        Pagina um: <br/>
                        <input type="hidden" class="form-control" value="{% if obra %}{{obra.paginaum}}{% else %}0{% endif %}" name="paginaum">
                    </div>
                    <div class="col-md-3 col-sm-4 hide">
                        Qtde. de Paginas: <br/>
                        <input type="hidden" class="form-control" value="{% if obra %}{{obra.qtdpaginas}}{% else %}0{% endif %}" name="qtdpaginas">
                    </div>
                </div>


                <div class="row">
                    <div class="col-md-12 text-center">
                        <a href="{{work_dir}}/" class="btn btn-default">Cancelar</a>
                        <button class="btn btn-success btn-salvar" data-loading-text="<span class='glyphicon glyphicon-refresh glyphicon-refresh-animate'></span> Salvando, aguarde ...">Salvar</button>
                    </div>
                </div>


    </form>



    <br/><br/><br/>
{% endblock %}
