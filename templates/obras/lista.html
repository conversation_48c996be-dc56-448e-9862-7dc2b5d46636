<span id="loading" style="display: none;margin: auto;
    text-align: center;
    width: 100%;
    border: 1px solid #dddddd;
    padding: 50px;">
<img src="/media/img/loading.gif" style="width:35px;"/> atualizando automaticamente...
</span>
<table class="table table-condensed table-striped table-hover" id="table-obras">

    <thead>
        <tr>
            <th width="50px">CAPA</th>
            <th width="50px">CODIGO</th>
            <th>TITULO</th>
            <th width="150px">PUBLICADA</th>
            <th width="320px">ETAPA</th>
        </tr>
    </thead>
            
    <tbody>

      {% for obra in obras %}
          <tr>
                
            <td>  <img src="/uploads/{{tenant.tenant}}/{{obra.id}}/capa.jpg" width="30px" /></td>

            <td>#<strong>{{ obra.id }}</strong></td>
                
                <td>{{ obra.titulo|highlight(filtros.busca) }}</td>


                <td>

                  {% if obra.status == 0 %} Rascunho <br/> <small> fora da produção </small>{% endif %}
                  

                  {% if obra.status == 1 %} Publicada <br/> <small>{{ obra.datapublicacao|ConverteData}}</small> {% endif %}
                  {% if obra.status == 2 %} Em venda <br/> <small>{{ obra.datapublicacao|ConverteData}}</small> {% endif %}

                </td>
                
                <td style="font-size:12px;">

                        <!-- sem PDF e com PDF -->

                              Etapa {{obra.etapa}} de 10 - 

                              {% if obra.etapa == 0 %} <strong> Enviar PDF </strong> {% endif %}
                              {% if obra.etapa == 1 %} Aguardando mastigador... {% endif %}
                              
                              {% if obra.etapa == 2 %} 

                                  <strong>Mastigando... </strong>

                                  {% if sincronizador.etapa_mastigar %}
                                    <small style="color: green"> (Executando)</small>
                                  {% else %}
                                    <small style="color: red"> (Parado)</small>
                                  {% endif %}

                                  <small>{{obra.mastigador.progress}}% - {{ obra.mastigador.pagina }}/{{ obra.qtdpaginas }}</small>

                                  <div class="progress">
                                    <div class="progress-bar progress-bar-info progress-bar-striped active" role="progressbar" aria-valuenow="{{obra.mastigador.progress}}" aria-valuemin="0" aria-valuemax="100" style="width: {{obra.mastigador.progress}}px">
                                    </div>
                                  </div>

                              {% endif %}
                              
                              {% if obra.etapa == 3 %} <strong>Aguardando tratamento...</strong> {% endif %}
                              {% if obra.etapa == 4 %} 

                                  <strong>Tratando... </strong>

                                  {% if sincronizador.etapa_tratar %}
                                    <small style="color: green"> (Executando)</small>
                                  {% else %}
                                    <small style="color: red"> (Parado)</small>
                                  {% endif %}

                                  <div class="progress">
                                    <div class="progress-bar progress-bar-success progress-bar-striped active" role="progressbar" aria-valuenow="{{obra.tratador.progress}}" aria-valuemin="0" aria-valuemax="100" style="width: {{obra.tratador.progress}}px">
                                    </div>
                                  </div>

                              {% endif %}

                              {% if obra.etapa == 5 %} <strong>Em montagem!</strong> {% endif %}

                              {% if obra.etapa == 6 %} <strong>Aguardando indexação...</strong> {% endif %}
                              
                              {% if obra.etapa == 7 %} 
                                  <strong>Indexando (JBE e IDX)... </strong>  
                                  {% if sincronizador.etapa_indexar %}
                                    <small style="color: green"> (Executando)</small>
                                  {% else %}
                                    <small style="color: red"> (Parado)</small>
                                  {% endif %}
                              {% endif %}

                              {% if obra.etapa == 8 %} <strong>Aguardando envio... {% endif %}
                              {% if obra.etapa == 9 %} <strong>Enviando...</strong> {% endif %}
                              {% if obra.etapa == 10 %} <strong>Enviada!</strong>   {% endif %}

                     
                        <div>
                            <!-- Só pode editar a obra para mandar o PDF, ou quando está em montagem, ou quando estiver completa -->

                                <a href="/obra/descompactar/{{ obra.id }}" title="descompactar" class="btn btn-xs btn-primary"><span class="glyphicon glyphicon-resize-full"></span></a>

                                <a href="/obra/compactar/{{ obra.id }}" title="compactar" class="btn btn-xs btn-primary"><span class="glyphicon glyphicon-resize-small"></span></a>

                            {% if obra.etapa == 0 or obra.etapa == 5 %}
                            <div class="btn-group">
                              <a href="/obra/cadastro-v2/{{ obra.id }}" class="btn btn-xs btn-primary" title="Editar (Versão Moderna)">
                                <span class="glyphicon glyphicon-edit"></span>
                              </a>
                              <button type="button" class="btn btn-xs btn-primary dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                              </button>
                              <ul class="dropdown-menu">
                                <li><a href="/obra/cadastro-v2/{{ obra.id }}"><i class="fa fa-star"></i> Editar (Moderno)</a></li>
                                <li><a href="/obra/cadastro/{{ obra.id }}"><i class="fa fa-edit"></i> Editar (Clássico)</a></li>
                              </ul>
                            </div>
                            {% endif %}
                            
                            <!-- acoes de correcao e sumario, so apos o mastigador -->
                            {% if obra.etapa == 5 or obra.etapa == 10 %}
                            <a href="/obra/ajustar/{{ obra.id }}" class="btn btn-xs btn-primary" data-toggle="tooltip" data-placement="left" title="" data-original-title="Ajustar">
                              <span class="glyphicon glyphicon-screenshot"></span> 
                            </a>                                    
                            
                            <a href="/obra/sumario/{{ obra.id }}" class="btn btn-xs btn-primary" data-toggle="tooltip" data-placement="left" title="" data-original-title="Sumário">
                              <span class="glyphicon glyphicon-align-right"></span>
                            </a>

                            |

                            {% endif %}

                            {% if obra.etapa == 0 or obra.etapa == 5 or obra.etapa == 10  %}
                              
                              <!-- Se estiver publicada de qualquer jeito, crio uma acao para voltar para montagem -->
                              {% if obra.status == 2 or obra.status == 1 %}
                                {% if obra.etapa != 5 %}
                                  <a href="/obra/remontar/{{ obra.id }}" class="link-ajax btn btn-xs btn-primary" data-toggle="tooltip" data-placement="left" title="" data-original-title="Sumário">
                                    <span class="glyphicon glyphicon-arrow-left"></span> montagem
                                  </a>
                                {% endif %}
                              {% endif %}

                              
                              {% if obra.status == 0 or obra.status == 1 %}
                              <a href="/obra/publicar/{{ obra.id }}/1" class="link-ajax btn btn-xs btn-success" data-toggle="tooltip" data-placement="left" title="" data-original-title="Sumário">
                                <span class="glyphicon glyphicon-share-alt"></span> {% if obra.status == 0 %} disp. {% else %} reenviar! {% endif %}
                              </a>
                              {% endif %}
                              
                              {% if obra.status == 0 or obra.status == 2 %}
                              <a href="/obra/publicar/{{ obra.id }}/2" class="link-ajax btn btn-xs btn-success" data-toggle="tooltip" data-placement="left" title="" data-original-title="Sumário"> 
                                <span class="glyphicon glyphicon-share-alt"></span> {% if obra.status == 0 %} vend. {% else %} reenviar! {% endif %}
                              </a> 
                              {% endif %}                          

                          {% endif %} 

			  <div style="float: right;" title="Excluir Obra!"><a href="/obra/excluir/{{ obra.id }}" data-idobra="{{ obra.id }}" class="excluir_obra" data-etapa="{{ obra.etapa }}"><i class="fa fa-close" style="color: red;"></i></a></div>


   {% if obra.etapa != 1  %}
   <a href="javascript:void(0);" title="Resetar Obra para Etapa 1 !" onclick="confirm('Deseja resetar o processamento desta obra?') ? window.location='/obra/resetar/{{obra.id}}' : alert('Ok');" class="btn-warning" style="float:right;color:goldenrod;background:white;margin-right:3px;"><span class="glyphicon glyphicon-repeat"></span></a>
			                                   {% endif %}
    {% if obra.etapa == 10 and 1==0 %}
    <a href="javascript:void(0);" title="Reenviar imagens da obra !" onclick="confirm('Deseja reenviar as imagens desta obra?') ? window.location='/obra/imagens/{{obra.id}}' : alert('Ok');" class="btn-success" style="float:right;color:green;background:white;margin-right:3px"><span class="glyphicon glyphicon-picture"></span></a>
    {% endif %}
    <!--    <a href="/obras/chatgpt/{{obra.id}}" title="Gerar Sumário com ChatGPT" class="btn btn-xs btn-success" style="background:white;border:1px solid #cdcdcd;margin-top:10px;color:black;" data-placement="left" data-toggle="tooltip"><span class="glyphicon glyphicon-book"></span> Sumário ChatGPT</a>-->
                        </div>

                </td>

          </tr>
      {% endfor %}

    </tbody>

  </table>

  <nav id="obras_paginacao" class="be-pagination text-center">
  <ul>
      <!-- Prev -->
      {% if current == 1 %}
          <li class="btn-pag disabled"><i class="fa fa-angle-left"></i>Anterior</li>
      {% else %}
          <li class="btn-pag obras-pagina-link obras-pagina-prev" data-pagina="{{anterior}}"><i class="fa fa-angle-left"></i>Anterior</li>
      {% endif %}

      <!-- Smart pages -->
      {% for pagina in paginas %}


            {% if current == pagina %}
                <li class="pag-active">Página {{pagina}} de {{total_paginas}} &nbsp;<small>({{total_registros}} e-books)</small> </a></li> 
            {% endif %}


      {% endfor %}

      <!-- Prev -->
      {% if current == total_paginas %}
          <li class="btn-pag disabled">Próxima<i class="fa fa-angle-right"></i></li>
      {% else %}
          <li class="btn-pag obras-pagina-link obras-pagina-next" data-pagina="{{proxima}}">Próxima<i class="fa fa-angle-right"></i></li>
      {% endif %}
  </ul>
  </nav>
