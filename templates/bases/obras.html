{% extends 'bases/base.html' %}

{% block javascript %}
          <script type="text/javascript" src="{{media_dir}}/js/obras.js?v={{version}}"></script>  
          <script>
            $(document).ready(function() {
                obras.init();
            });
          </script>
{% endblock %}

{% block css %}
    <link rel="stylesheet" type="text/css" href="{{media_dir}}/css/obras.css?v={{version}}" />
{% endblock %}

{% block body_class %}dashboard-content{% endblock %}

{% block main %}

{% include 'modals/excluir_obra_naopode.html' %}

{% include 'modals/excluir_obra_confirma.html' %}


  <section id="obras">
            
          <h4> <i class="text-pc fa fa-th-large"></i> Listagem de e-books </h4>


          <div class="row filtros">


            <div class="col-md-2">
              <small></small><br/>
              <div class="btn-group">
                <a href="/obra/cadastro" class="btn btn-success">
                  <i class="fa fa-plus"></i> Nova Obra
                </a>
                <button type="button" class="btn btn-success dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                  <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                  <li><a href="/obra/cadastro"><i class="fa fa-edit"></i> Cadastro</a></li>
                </ul>
              </div>
            </div>

            <div class="col-md-7">
              <small>Buscar:</small>
              <input class="form-control filtro-busca" type="text" />
            </div>


            <div class="col-md-3">
              <small>Status:</small>
              <select class="form-control filtro-status">
                  <option value="">Todas status</option>
                  <option value="0">Rascunho</option>
                  <option value="1">Publicada sem venda</option>
                  <option value="2">Publicada com venda</option>
              </select>
            </div>

            <!--
            <div class="col-md-3">
              <small>Etapa:</small>
              <select class="form-control filtro-etapa">
                  <option value="">Todas etapas</option>
                  <option value="0">Sem PDF</option>
                  <option value="1">Aguardando mastigador</option>
                  <option value="2">Mastigando</option>
                  <option value="3">Aguardando tratamento</option>
                  <option value="4">Tratando</option>
                  <option value="5">Em montagem</option>
                  <option value="6">Aguardando indexação</option>
                  <option value="7">Indexando (JBE e IDX)</option>
                  <option value="8">Aguardando envio</option>
                  <option value="9">Enviando</option>
                  <option value="10">Enviada</option>
              </select>            
            </div>
            -->

	    <div class="col-md-4" style="margin-top:25px;text-decoration:underline;" title="Ativar atualização automática da tela">
		    <input type="checkbox" id="aupdate"/> &nbsp;<span class="glyphicon glyphicon-refresh"></span> Auto-Refresh
	    </div>
            <div class="col-md-8 filtro-status" style="margin-top:25px; font-size:20px;">
                  <span class="label label-default active" data-status="3">Todas  </span>
                  <span class="label label-danger" data-status="1">Em Processamento  </span>
                  <span class="label label-primary" data-status="0">Em montagem  </span> 
                  <span class="label label-success" data-status="2">Concluidas  </span>
            </div>


          </div>

           <hr/>

          <div id="obras_listar"></div>
  </section>
          
{% endblock %}

