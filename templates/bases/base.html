<!DOCTYPE html>
<html lang="pt-br">
    
    <head>

        <title>
            {% block page_title %} {{tenant.nome}} | Prateleira {% endblock %}
        </title>

        <!-- Metas -->
        <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
        <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1">

        <!-- Robots -->
        <meta name="rating" content="General">
        <meta name="robots" content="follow" />
        <meta name="revisit-after" content="1" />

        <!-- SEO - Keywords -->
        <meta name="description" content="{% block page_description %}{% endblock %}">
        <meta name="keywords" content="{% block page_keywords %}{% endblock %}">

        <!-- Favicon -->
        {% block meta_favicon %}
            <link rel="shortcut icon" href="{{media_dir}}/img/favico.png"> 
        {% endblock %}

        <!-- CSS core: <PERSON><PERSON><PERSON>, Datepic<PERSON>, Plugins e etc -->
        {% block core_css %}

            <link rel="stylesheet" type="text/css" href="{{media_dir}}/core_css/font-awesome.css?v={{version}}" />
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/core_css/font-source-sans.css?v={{version}}" />
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/core_css/bootstrap.min.css?v={{version}}" />
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/core_css/perfect-scrollbar.css?v={{version}}" />
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/css/obras.css?v={{version}}" />
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/css/bootstrap-switch.min.css?v={{version}}" />
            <link rel="stylesheet" type="text/css" href="{{media_dir}}/css/select2.min.css?v={{version}}" />


        {% endblock %}

        <!-- CSS Adicionais de páginas -->
        {% block css %}{% endblock %}

        <!-- CSS de cores da tenant -->
        <link rel="stylesheet" type="text/css" href="{{media_dir}}/generate_css/{{tenant.tenant}}.css" />

        <!-- CSS Adicionais de tenants -->
        {% block css_tenants %}{% endblock %}

    </head>
    
    <body class="{% block body_class %}dashboard-content{% endblock %}">
    

        <div class="dashboard-holder">

            {% block header_outer %}
            <header class="main-header">
                {% block header %}
                    {% include 'includes/header.html' %}
                {% endblock %}
            </header>
            {% endblock %}

            <!-- MAIN -->
            {% block main_outer %}
            <div class="main-content">


                    <main class="container">
                        {% block main %}
                        {% endblock %}
                    </main>

            </div>
            {% endblock %}
        </div>


    <!-- Modal`s -->
    <div id="modal" class="modal fade be-modal" tabindex="-1" role="dialog" aria-hidden="true"></div>

    <!-- Page Loader -->
    <div class="page-loader hide">
        <div class="sk-circle mini">
          <div class="sk-circle1 sk-child"></div>
          <div class="sk-circle2 sk-child"></div>
          <div class="sk-circle3 sk-child"></div>
          <div class="sk-circle4 sk-child"></div>
          <div class="sk-circle5 sk-child"></div>
          <div class="sk-circle6 sk-child"></div>
          <div class="sk-circle7 sk-child"></div>
          <div class="sk-circle8 sk-child"></div>
          <div class="sk-circle9 sk-child"></div>
          <div class="sk-circle10 sk-child"></div>
          <div class="sk-circle11 sk-child"></div>
          <div class="sk-circle12 sk-child"></div>
        </div>
        <span>Carregando...</span>
    </div>

    <div class="printer"></div>

    {% block core_javascript %}
        <script type="text/javascript" src="{{media_dir}}/core_js/jquery.min.js?v={{version}}"></script>
        <script type="text/javascript" src="{{media_dir}}/core_js/jquery.ui.min.js?v={{version}}"></script>
        <script type="text/javascript" src="{{media_dir}}/core_js/bootstrap.min.js?v={{version}}"></script>
        <script type="text/javascript" src="{{media_dir}}/core_js/jPushMenu.js?v={{version}}"></script>  
        <script type="text/javascript" src="{{media_dir}}/core_js/jquery.cookie.js?v={{version}}"></script>  
        <script type="text/javascript" src="{{media_dir}}/core_js/jquery.form.js?v={{version}}"></script>  
        <script type="text/javascript" src="{{media_dir}}/core_js/shortcut.js?v={{version}}"></script>  
        <script type="text/javascript" src="{{media_dir}}/core_js/perfect-scrollbar.jquery.js?v={{version}}"></script>  
        <script type="text/javascript" src="{{media_dir}}/js/bootstrap-switch.min.js?v={{version}}"></script>  
        <script type="text/javascript" src="{{media_dir}}/core_js/select2.min.js?v={{version}}"></script>  
        <script>
        jQuery(function(){
		jQuery('.addIndex').on('click',function(e){
		e.preventDefault();
		    var $elm = jQuery(this).parent().parent('.indexes')

		    var $newRow = $elm.clone(); //clone it
		    $newRow.find(":text").val(""); //clear out textbox values    
		    $elm.after($newRow); //add in the new row at the end
		});
		
		jQuery('.removeIndex').on('click',function(e){
			e.preventDefault();
		    var $elm = jQuery(this).parent().parent('.indexes')
		    $elm.remove().fadeOut();
		});
		
		//SALVA SUMARIO		
		jQuery('#saveIndex').on('click',function(e){
			e.preventDefault();
			jQuery(this).attr('disabled','disabled');
			jQuery('.indexes').each(function(){
			
				var nivel = jQuery(this).find('.nivel').val();
				var pagina = jQuery(this).find('.pagina').val();
				var indice = jQuery(this).find('.indice').val();
				var idobra = jQuery(this).attr('data-idobra');
			
			
				jQuery.post('/obra/sumario/'+idobra,{nivel:nivel,pagina:pagina,indice:indice},function(data){

				},'json');
			});
			
			setTimeout(function(){
			   location.reload();
			}, 5000);
		});
		//RESETA SUMARIO		
		jQuery('#clearIndex').on('click',function(e){
			e.preventDefault();
			var idobra = jQuery(this).attr('data-idobra');
			
			jQuery.post('/obra/sumario/'+idobra,{clearIndex:1},function(data){
					location.reload();
				},'json');
		});
		
		
        });
       
        </script>
    {% endblock %}

    {% block javascript %}
    {% endblock %}

    {% if tenant.codigo_ga is defined and tenant.codigo_ga %}
    <script>
      (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
      (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
      m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
      })(window,document,'script','//www.google-analytics.com/analytics.js','ga');

      ga('create', '{{tenant.codigo_ga}}', 'auto');
      ga('send', 'pageview');

    </script>
    {% endif %}


</body>
</html>
