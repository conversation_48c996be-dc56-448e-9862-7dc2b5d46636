<?php

use Swoole\Http\Server;
use Swoole\Http\Request;
use Swoole\Http\Response;

// Configurações do servidor
$host = '0.0.0.0';
$port = 9501;

// Incluir classe de sessão personalizada
require_once '/var/www/html/classes/SwooleSession.php';

// Criar servidor HTTP
$server = new Server($host, $port);

// Configurações do servidor
$server->set([
    'worker_num' => (int)($_ENV['SWOOLE_WORKERS'] ?? 2),
    'daemonize' => false,
    'max_request' => (int)($_ENV['SWOOLE_MAX_REQUESTS'] ?? 1000),
    'dispatch_mode' => 2,
    'debug_mode' => ($_ENV['APP_ENV'] === 'development') ? 1 : 0,
    'enable_static_handler' => true,
    'document_root' => '/var/www/html',
    'http_compression' => true,
    // Configurações para desenvolvimento
    'reload_async' => true,
    'max_wait_time' => 60,
    'enable_reuse_port' => true,
    'heartbeat_check_interval' => 60,
    'heartbeat_idle_time' => 600,
    // Configurações para upload de arquivos grandes
    'package_max_length' => 500 * 1024 * 1024, // 500MB
    'buffer_output_size' => 32 * 1024 * 1024,  // 32MB
    'input_buffer_size' => 32 * 1024 * 1024,   // 32MB
    'http_parse_post' => true,
]);

// Callback para requisições HTTP
$server->on('request', function (Request $request, Response $response) {
    
    // Configurar headers CORS se necessário
    $response->header('Access-Control-Allow-Origin', '*');
    $response->header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    $response->header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With');
    
    // Tratar requisições OPTIONS (preflight)
    if ($request->server['request_method'] === 'OPTIONS') {
        $response->status(200);
        $response->end();
        return;
    }
    
    // Configurar variáveis globais para compatibilidade
    $_SERVER = array_merge($_SERVER ?? [], $request->server ?? []);
    $_GET = $request->get ?? [];
    $_POST = $request->post ?? [];
    $_COOKIE = $request->cookie ?? [];
    $_FILES = $request->files ?? [];
    $_REQUEST = array_merge($_GET, $_POST, $_COOKIE);
    
    // Configurar headers HTTP
    foreach ($request->header ?? [] as $key => $value) {
        $_SERVER['HTTP_' . strtoupper(str_replace('-', '_', $key))] = $value;
    }
    
    // Configurar REQUEST_URI
    $_SERVER['REQUEST_URI'] = $request->server['request_uri'] ?? '/';
    $_SERVER['REQUEST_METHOD'] = $request->server['request_method'] ?? 'GET';
    $_SERVER['HTTP_HOST'] = $request->header['host'] ?? 'localhost';
    $_SERVER['DOCUMENT_ROOT'] = '/var/www/html'; // Definir DOCUMENT_ROOT para compatibilidade

    // Health check endpoint
    if ($_SERVER['REQUEST_URI'] === '/health') {
        $health_data = [
            'status' => 'ok',
            'timestamp' => date('Y-m-d H:i:s'),
            'environment' => $_ENV['APP_ENV'] ?? 'unknown',
            'swoole_version' => swoole_version(),
            'php_version' => PHP_VERSION,
            'memory_usage' => memory_get_usage(true),
            'memory_peak' => memory_get_peak_usage(true)
        ];

        $response->header('Content-Type', 'application/json');
        $response->end(json_encode($health_data, JSON_PRETTY_PRINT));
        return;
    }

    // Inicializar sessão personalizada
    $swooleSession = new SwooleSession();
    $swooleSession->start($response);

    // Simular $_SESSION para compatibilidade
    $_SESSION = $swooleSession->getData();

    if ($_ENV['APP_ENV'] === 'development') {
        error_log("Swoole Debug: Session ID = " . $swooleSession->getId());
        error_log("Swoole Debug: Session data = " . print_r($_SESSION, true));
    }

    // Debug: Log do host recebido (apenas em desenvolvimento)
    if ($_ENV['APP_ENV'] === 'development') {
        error_log("Swoole Debug: Host recebido = " . $_SERVER['HTTP_HOST']);
        error_log("Swoole Debug: URI = " . $_SERVER['REQUEST_URI']);
        error_log("Swoole Debug: Method = " . $_SERVER['REQUEST_METHOD']);
        error_log("Swoole Debug: POST data = " . print_r($_POST, true));
        error_log("Swoole Debug: GET data = " . print_r($_GET, true));
        error_log("Swoole Debug: COOKIE data = " . print_r($_COOKIE, true));
        error_log("Swoole Debug: SESSION data = " . print_r($_SESSION, true));
        error_log("Swoole Debug: REQUEST data = " . print_r($_REQUEST, true));
    }
    
    // Capturar output
    ob_start();
    
    try {
        // Incluir o index.php da aplicação
        include '/var/www/html/index.php';
        
        $content = ob_get_contents();
        ob_end_clean();
        
        // Salvar sessão personalizada
        $swooleSession->setData($_SESSION);
        $swooleSession->save();

        if ($_ENV['APP_ENV'] === 'development') {
            error_log("Swoole Debug: Session saved with ID: " . $swooleSession->getId());
        }

        // Enviar resposta
        $response->header('Content-Type', 'text/html; charset=utf-8');
        $response->end($content);
        
    } catch (Throwable $e) {
        ob_end_clean();
        
        // Log do erro
        error_log("Swoole Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());
        
        // Resposta de erro
        $response->status(500);
        $response->header('Content-Type', 'application/json');
        $response->end(json_encode([
            'error' => 'Internal Server Error',
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine()
        ]));
    }
    
    // Limpar variáveis globais
    $_GET = $_POST = $_COOKIE = $_FILES = [];
});

// Callback para inicialização do worker
$server->on('workerStart', function ($server, $workerId) {
    if ($_ENV['APP_ENV'] === 'development') {
        echo "Worker {$workerId} started\n";
    }
});

// Callback para início do servidor
$server->on('start', function ($server) {
    echo "Swoole HTTP Server started at http://{$server->host}:{$server->port}\n";
});

// Iniciar servidor
$server->start();
