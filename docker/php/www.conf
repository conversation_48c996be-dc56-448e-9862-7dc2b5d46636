[www]
; Pool name
user = www-data
group = www-data

; Listen on TCP socket
listen = 9000

; Listen owner/group
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

; Process manager
pm = dynamic

; Maximum number of child processes
pm.max_children = 20

; Number of child processes created on startup
pm.start_servers = 3

; Minimum number of spare idle child processes
pm.min_spare_servers = 2

; Maximum number of spare idle child processes
pm.max_spare_servers = 6

; Maximum number of requests each child process should execute
pm.max_requests = 1000

; Status path
pm.status_path = /status

; Ping path
ping.path = /ping

; Access log
access.log = /var/log/php-fpm-access.log

; Slow log
slowlog = /var/log/php-fpm-slow.log
request_slowlog_timeout = 10s

; Environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

; PHP admin values
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f <EMAIL>
php_flag[display_errors] = on
php_admin_value[error_log] = /var/log/php-fpm-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 1G
php_admin_value[upload_max_filesize] = 500M
php_admin_value[post_max_size] = 500M
php_admin_value[max_execution_time] = 300
php_admin_value[max_input_time] = 300

; Security
php_admin_value[disable_functions] = exec,passthru,shell_exec,system
; php_admin_value[open_basedir] = /var/www/html:/tmp
