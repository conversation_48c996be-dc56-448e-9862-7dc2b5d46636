<?php

namespace Views\Login;

/* Método para autenticar o usuário via login e senha */
class Logout {

    function GET($matches, $app) {
        // Finalizar sessão
        unset($app->session["logado"]);
        unset($app->session["usuario_logado"]);
        unset($app->session["usuario_logado_chave"]);
        
        $app->atualizaSessao();
        
        // Redirecionar para raiz (compatível com Swoole)
        if (!headers_sent()) {
            header("Location: /");
            header("Cache-Control: no-cache, no-store, must-revalidate");
            header("Pragma: no-cache");
            header("Expires: 0");
        }
        
        // Em ambiente Swoole, não usar exit()
        return;
    }

}

class Login {

    function GET($matches, $app) {

        if(isset($app->session["logado"]) && $app->session["logado"] == true) $app->redir("/obras");

        error_log("Login Debug: Tentando carregar template login.html");
        $template = $app->template("login.html");
        if ($template === null) {
            error_log("Login Debug: Template retornou null");
            return;
        }

        // Verificar se há mensagem de erro nos parâmetros (fallback)
        $erro_msg = isset($_GET['erro']) ? $_GET['erro'] : null;
        $template->set("erro_msg", $erro_msg);
        if ($erro_msg) {
            error_log("Login Debug: Mensagem de erro definida: " . $erro_msg);
        }

        error_log("Login Debug: Template carregado com sucesso, tentando renderizar");
        echo $template->render();
        error_log("Login Debug: Template renderizado com sucesso");
    }

}

/* Método para autenticar o usuário via login e senha */
class Autenticar {

    # Before - apenas exige AJAX se não for fallback
    public $before = array();

    function POST($matches, $app) {
        $email = trim($_REQUEST["login-email"]);
        $senha = trim($_REQUEST["login-senha"]);
        $isFallback = isset($_REQUEST["fallback"]) && $_REQUEST["fallback"] == "1";

        error_log("Login Debug: Tentativa de login - Email: " . $email . " | Fallback: " . ($isFallback ? "SIM" : "NÃO"));

        // Se não for fallback, verificar se é AJAX
        if (!$isFallback) {
            if(!$app->settings["debug"] && !IS_AJAX) {
                $app->error("404");
                return;
            }
        }

        # Verifica os campos recebidos
        if(empty($email)) {
            error_log("Login Debug: Email vazio");

            if($isFallback) {
                // Redirecionamento com erro para fallback
                if (!headers_sent()) {
                    header("Location: /?erro=" . urlencode('Campo email não deve estar vazio.'));
                }
                return;
            } else {
                $app->retornaJson(-1, 'Campo email não deve estar vazio.');
                return;
            }
        }

        if(empty($senha)) {
            error_log("Login Debug: Senha vazia");

            if($isFallback) {
                // Redirecionamento com erro para fallback
                if (!headers_sent()) {
                    header("Location: /?erro=" . urlencode('Campo senha não deve estar vazio.'));
                }
                return;
            } else {
                $app->retornaJson(-2, 'Campo senha não deve estar vazio.');
                return;
            }
        }

        error_log("Login Debug: Campos validados, criando objeto Login");

        # Objeto de Login
        $login = new \Login();

        # Informações do Usuario
        $login->setUsuario($email);
        $login->setSenha($senha);

        error_log("Login Debug: Usuario e senha definidos no objeto Login, tentando autenticar");

        # Tenta autenticar
        $usuario = null;
        $auth = $login->login($usuario);

        error_log("Login Debug: Resultado da autenticação: " . $auth);
        error_log("Login Debug: Dados do usuario retornado: " . print_r($usuario, true));

        # Usuario autenticado.
        if($auth == 0):
            error_log("Login Debug: Autenticação bem-sucedida, autorizando usuário");
            $logado = $app->autorizaUsuarioLogado($usuario);
            error_log("Login Debug: Usuario autorizado com resultado: " . $logado);

            if($isFallback) {
                // Redirecionamento direto para fallback
                if (!headers_sent()) {
                    header("Location: /obras");
                }
                return;
            } else {
                $app->retornaJson(0, "Usuário autenticado.");
                return;
            }
        endif;

        error_log("Login Debug: Falha na autenticação - código: " . $auth);

        if($isFallback) {
            // Redirecionamento com erro para fallback
            if (!headers_sent()) {
                header("Location: /?erro=" . urlencode('Usuário e/ou senha incorretos.'));
            }
            return;
        } else {
            $app->retornaJson(-3, "Usuário e/ou senha incorretos.");
            return;
        }

    }

}



?>
